import { MonsterTidal } from "./MonsterTidal";

const { ccclass } = cc._decorator;

@ccclass
class MonsterTidalBoss extends MonsterTidal {
    public init(): void {
        super.init();
        this.game.createLifeBar(this, {
            scale: 2 * this.monCfg.Scale
        });
    }

    public onNewSize(size: cc.Size): void {
        super.onNewSize(size);
        this._haedPosition.y /= 0.7;
        this._haedPosition.y *= 1.1;
    }
}

export default MonsterTidalBoss;
