import { GTSimpleSpriteAssembler2D } from "./GTSimpleSpriteAssembler2D";

const gfx = cc.gfx;

const vertexFormat = new gfx.VertexFormat([
    {
        name: gfx.ATTR_POSITION,
        type: gfx.ATTR_TYPE_FLOAT32,
        num: 2
    },
    {
        name: gfx.ATTR_UV0,
        type: gfx.ATTR_TYPE_FLOAT32,
        num: 2
    },
    {
        name: gfx.ATTR_UV1,
        type: gfx.ATTR_TYPE_FLOAT32,
        num: 2
    },
    {
        name: "a_p",
        type: gfx.ATTR_TYPE_FLOAT32,
        num: 2
    },
    {
        name: "a_q",
        type: gfx.ATTR_TYPE_FLOAT32,
        num: 2
    }
]);

const tempVec = cc.Vec2.ZERO;

class MovingBGAssembler extends GTSimpleSpriteAssembler2D {
    public verticesCount: number = 4;
    public indicesCount: number = 6;

    get vertexFormat(): any {
        return vertexFormat;
    }

    public updateVerts(sprite: any): void {
        const node = sprite.node;
        const cw = node.width;
        const ch = node.height;
        const appx = node.anchorX * cw;
        const appy = node.anchorY * ch;
        
        let l = -appx;
        let b = -appy;
        let r = cw - appx;
        let t = ch - appy;

        const local = this._local;
        local[0] = l;
        local[1] = b;
        local[2] = r;
        local[3] = t;

        this.updateWorldVerts(sprite);
    }

    public fillBuffers(comp: any, renderer: any): void {
        const node = comp.node;
        const buffer = renderer.getBuffer('mesh', this.vertexFormat);
        const vertexOffset = buffer.byteOffset >> 2;
        const vertexCount = this.verticesCount;

        let vertexId = buffer.request(vertexCount, this.indicesCount);
        const vbuf = buffer._vData;
        const ibuf = buffer._iData;

        // Fill vertex data
        for (let i = 0; i < vertexCount; i++) {
            const vert = this._renderData.vDatas[i];
            let offset = vertexId + i * this.floatsPerVert;
            
            vbuf[offset++] = vert.x;
            vbuf[offset++] = vert.y;
            vbuf[offset++] = vert.u;
            vbuf[offset++] = vert.v;
            vbuf[offset++] = vert.u1 || 0;
            vbuf[offset++] = vert.v1 || 0;
            vbuf[offset++] = vert.p || 0;
            vbuf[offset++] = vert.q || 0;
        }

        // Fill index data
        const indexOffset = buffer.indiceOffset;
        const indices = this.indices;
        for (let i = 0; i < this.indicesCount; i++) {
            ibuf[indexOffset + i] = vertexId + indices[i];
        }
    }

    public updateUVs(sprite: any): void {
        const uv = sprite._spriteFrame.uv;
        const uvOffset = this.uvOffset;
        const floatsPerVert = this.floatsPerVert;
        const verts = this._renderData.vDatas;

        for (let i = 0; i < 4; i++) {
            const srcOffset = i * 2;
            const dstOffset = floatsPerVert * i + uvOffset;
            verts[dstOffset] = uv[srcOffset];
            verts[dstOffset + 1] = uv[srcOffset + 1];
        }
    }

    public updateColor(sprite: any, color: cc.Color): void {
        // Color update implementation if needed
    }
}

export default MovingBGAssembler;
