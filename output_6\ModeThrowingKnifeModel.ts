import { MVC } from "./MVC";
import { Game } from "./Game";

class ModeThrowingKnifeModel extends MVC.BaseModel {
    private static _instance: ModeThrowingKnifeModel = null;
    public gameMode: Game.Mode = Game.Mode.THROWINGKNIFE;

    constructor() {
        super();
        if (!ModeThrowingKnifeModel._instance) {
            ModeThrowingKnifeModel._instance = this;
        }
    }

    public reset(): void {}

    static get instance(): ModeThrowingKnifeModel {
        if (!ModeThrowingKnifeModel._instance) {
            ModeThrowingKnifeModel._instance = new ModeThrowingKnifeModel();
        }
        return ModeThrowingKnifeModel._instance;
    }
}

export default ModeThrowingKnifeModel;
