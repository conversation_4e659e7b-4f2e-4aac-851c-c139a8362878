// MDefense.ts
// 从 MDefense.js 转换而来

import { GameSeting } from "./GameSeting";
import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { NodePool } from "./NodePool";
import { CompManager } from "./CompManager";
import { Time } from "./Time";

/**
 * 防御塔类型枚举
 */
export enum TowerType {
    BASIC = "basic",
    CANNON = "cannon",
    LASER = "laser",
    MISSILE = "missile",
    FREEZE = "freeze",
    POISON = "poison"
}

/**
 * 防御游戏模式管理器
 */
export class MDefense extends GameSeting.CompBase {
    /** 防御塔列表 */
    private towers: any[] = [];
    
    /** 敌人列表 */
    private enemies: any[] = [];
    
    /** 路径点 */
    private pathPoints: cc.Vec2[] = [];
    
    /** 当前波次 */
    private currentWave: number = 1;
    
    /** 玩家生命值 */
    private playerHealth: number = 100;
    
    /** 玩家金币 */
    private playerGold: number = 500;
    
    /** 游戏状态 */
    private gameState: string = "preparing";
    
    /** 波次计时器 */
    private waveTimer: number = 0;
    
    /** 敌人生成间隔 */
    private enemySpawnInterval: number = 1.0;
    
    /** 敌人生成计时器 */
    private enemySpawnTimer: number = 0;
    
    /** 当前波次敌人数量 */
    private currentWaveEnemyCount: number = 0;
    
    /** 已生成敌人数量 */
    private spawnedEnemyCount: number = 0;
    
    constructor() {
        super();
        this.initializeDefense();
        this.setupEventListeners();
    }
    
    /**
     * 初始化防御游戏
     */
    private initializeDefense(): void {
        this.loadPathPoints();
        this.resetGameState();
    }
    
    /**
     * 加载路径点
     */
    private loadPathPoints(): void {
        // 从配置中加载路径点
        this.pathPoints = [
            cc.v2(-400, 0),
            cc.v2(-200, 100),
            cc.v2(0, 100),
            cc.v2(200, 0),
            cc.v2(400, -100),
            cc.v2(600, 0)
        ];
    }
    
    /**
     * 重置游戏状态
     */
    private resetGameState(): void {
        this.currentWave = 1;
        this.playerHealth = 100;
        this.playerGold = 500;
        this.gameState = "preparing";
        this.waveTimer = 0;
        this.enemySpawnTimer = 0;
        this.currentWaveEnemyCount = 0;
        this.spawnedEnemyCount = 0;
        
        this.clearAllTowers();
        this.clearAllEnemies();
    }
    
    /**
     * 设置事件监听
     */
    private setupEventListeners(): void {
        Notifier.on(ListenID.BUILD_TOWER, this.onBuildTower, this);
        Notifier.on(ListenID.UPGRADE_TOWER, this.onUpgradeTower, this);
        Notifier.on(ListenID.SELL_TOWER, this.onSellTower, this);
        Notifier.on(ListenID.START_WAVE, this.onStartWave, this);
        Notifier.on(ListenID.ENEMY_REACHED_END, this.onEnemyReachedEnd, this);
        Notifier.on(ListenID.ENEMY_KILLED, this.onEnemyKilled, this);
    }
    
    /**
     * 更新游戏
     * @param dt 时间间隔
     */
    update(dt: number): void {
        if (this.gameState === "playing") {
            this.updateWave(dt);
            this.updateTowers(dt);
            this.updateEnemies(dt);
            this.checkWaveComplete();
        }
    }
    
    /**
     * 更新波次
     * @param dt 时间间隔
     */
    private updateWave(dt: number): void {
        this.waveTimer += dt;
        this.enemySpawnTimer += dt;
        
        // 生成敌人
        if (this.spawnedEnemyCount < this.currentWaveEnemyCount && 
            this.enemySpawnTimer >= this.enemySpawnInterval) {
            this.spawnEnemy();
            this.enemySpawnTimer = 0;
        }
    }
    
    /**
     * 生成敌人
     */
    private spawnEnemy(): void {
        const enemyConfig = this.getEnemyConfigForWave(this.currentWave);
        const enemy = this.createEnemy(enemyConfig);
        
        if (enemy) {
            this.enemies.push(enemy);
            this.spawnedEnemyCount++;
        }
    }
    
    /**
     * 获取波次敌人配置
     * @param wave 波次
     * @returns 敌人配置
     */
    private getEnemyConfigForWave(wave: number): any {
        return {
            type: "basic",
            health: 50 + wave * 10,
            speed: 100,
            reward: 10 + wave * 2
        };
    }
    
    /**
     * 创建敌人
     * @param config 敌人配置
     * @returns 敌人实例
     */
    private createEnemy(config: any): any {
        const enemy = NodePool.get("DefenseEnemy");
        if (enemy) {
            enemy.initWithConfig(config);
            enemy.setPath(this.pathPoints);
            enemy.setPosition(this.pathPoints[0]);
            return enemy;
        }
        return null;
    }
    
    /**
     * 更新防御塔
     * @param dt 时间间隔
     */
    private updateTowers(dt: number): void {
        this.towers.forEach(tower => {
            tower.update(dt);
            
            // 寻找目标
            const target = this.findTargetForTower(tower);
            if (target) {
                tower.setTarget(target);
                tower.attack();
            }
        });
    }
    
    /**
     * 为防御塔寻找目标
     * @param tower 防御塔
     * @returns 目标敌人
     */
    private findTargetForTower(tower: any): any {
        let closestEnemy = null;
        let closestDistance = tower.getRange();
        
        this.enemies.forEach(enemy => {
            const distance = cc.Vec2.distance(tower.getPosition(), enemy.getPosition());
            if (distance <= tower.getRange() && distance < closestDistance) {
                closestEnemy = enemy;
                closestDistance = distance;
            }
        });
        
        return closestEnemy;
    }
    
    /**
     * 更新敌人
     * @param dt 时间间隔
     */
    private updateEnemies(dt: number): void {
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];
            enemy.update(dt);
            
            // 检查敌人是否到达终点
            if (enemy.hasReachedEnd()) {
                this.onEnemyReachedEnd({ enemy: enemy });
            }
            
            // 检查敌人是否死亡
            if (enemy.isDead()) {
                this.onEnemyKilled({ enemy: enemy });
            }
        }
    }
    
    /**
     * 检查波次是否完成
     */
    private checkWaveComplete(): void {
        if (this.spawnedEnemyCount >= this.currentWaveEnemyCount && this.enemies.length === 0) {
            this.onWaveComplete();
        }
    }
    
    /**
     * 波次完成
     */
    private onWaveComplete(): void {
        this.gameState = "preparing";
        this.currentWave++;
        
        // 给予波次奖励
        this.playerGold += 100 + this.currentWave * 20;
        
        // 发送波次完成通知
        Notifier.send(ListenID.WAVE_COMPLETE, {
            wave: this.currentWave - 1,
            reward: 100 + (this.currentWave - 1) * 20
        });
        
        this.updateUI();
    }
    
    /**
     * 建造防御塔事件
     * @param data 事件数据
     */
    private onBuildTower(data: any): void {
        const { position, towerType } = data;
        const cost = this.getTowerCost(towerType);
        
        if (this.playerGold >= cost) {
            const tower = this.createTower(towerType, position);
            if (tower) {
                this.towers.push(tower);
                this.playerGold -= cost;
                this.updateUI();
                
                // 发送建造成功通知
                Notifier.send(ListenID.TOWER_BUILT, {
                    tower: tower,
                    cost: cost
                });
            }
        } else {
            // 金币不足
            Notifier.send(ListenID.INSUFFICIENT_GOLD, { required: cost, current: this.playerGold });
        }
    }
    
    /**
     * 获取防御塔建造费用
     * @param towerType 防御塔类型
     * @returns 建造费用
     */
    private getTowerCost(towerType: TowerType): number {
        const costs = {
            [TowerType.BASIC]: 50,
            [TowerType.CANNON]: 100,
            [TowerType.LASER]: 150,
            [TowerType.MISSILE]: 200,
            [TowerType.FREEZE]: 120,
            [TowerType.POISON]: 130
        };
        
        return costs[towerType] || 50;
    }
    
    /**
     * 创建防御塔
     * @param towerType 防御塔类型
     * @param position 位置
     * @returns 防御塔实例
     */
    private createTower(towerType: TowerType, position: cc.Vec2): any {
        const tower = NodePool.get("DefenseTower");
        if (tower) {
            tower.initWithType(towerType);
            tower.setPosition(position);
            return tower;
        }
        return null;
    }
    
    /**
     * 升级防御塔事件
     * @param data 事件数据
     */
    private onUpgradeTower(data: any): void {
        const { tower } = data;
        const upgradeCost = tower.getUpgradeCost();
        
        if (this.playerGold >= upgradeCost) {
            tower.upgrade();
            this.playerGold -= upgradeCost;
            this.updateUI();
            
            // 发送升级成功通知
            Notifier.send(ListenID.TOWER_UPGRADED, {
                tower: tower,
                cost: upgradeCost
            });
        }
    }
    
    /**
     * 出售防御塔事件
     * @param data 事件数据
     */
    private onSellTower(data: any): void {
        const { tower } = data;
        const sellPrice = tower.getSellPrice();
        
        // 移除防御塔
        const index = this.towers.indexOf(tower);
        if (index !== -1) {
            this.towers.splice(index, 1);
            this.playerGold += sellPrice;
            
            // 回收到对象池
            NodePool.put("DefenseTower", tower);
            
            this.updateUI();
            
            // 发送出售成功通知
            Notifier.send(ListenID.TOWER_SOLD, {
                tower: tower,
                price: sellPrice
            });
        }
    }
    
    /**
     * 开始波次事件
     */
    private onStartWave(): void {
        this.gameState = "playing";
        this.waveTimer = 0;
        this.enemySpawnTimer = 0;
        this.spawnedEnemyCount = 0;
        this.currentWaveEnemyCount = 10 + this.currentWave * 2;
        
        // 发送波次开始通知
        Notifier.send(ListenID.WAVE_STARTED, {
            wave: this.currentWave,
            enemyCount: this.currentWaveEnemyCount
        });
    }
    
    /**
     * 敌人到达终点事件
     * @param data 事件数据
     */
    private onEnemyReachedEnd(data: any): void {
        const { enemy } = data;
        
        // 扣除生命值
        this.playerHealth -= enemy.getDamage();
        
        // 移除敌人
        this.removeEnemy(enemy);
        
        // 检查游戏是否结束
        if (this.playerHealth <= 0) {
            this.onGameOver();
        }
        
        this.updateUI();
    }
    
    /**
     * 敌人被击杀事件
     * @param data 事件数据
     */
    private onEnemyKilled(data: any): void {
        const { enemy } = data;
        
        // 给予金币奖励
        this.playerGold += enemy.getReward();
        
        // 移除敌人
        this.removeEnemy(enemy);
        
        this.updateUI();
    }
    
    /**
     * 移除敌人
     * @param enemy 敌人实例
     */
    private removeEnemy(enemy: any): void {
        const index = this.enemies.indexOf(enemy);
        if (index !== -1) {
            this.enemies.splice(index, 1);
            NodePool.put("DefenseEnemy", enemy);
        }
    }
    
    /**
     * 游戏结束
     */
    private onGameOver(): void {
        this.gameState = "gameover";
        
        // 发送游戏结束通知
        Notifier.send(ListenID.GAME_OVER, {
            wave: this.currentWave,
            score: this.calculateScore()
        });
    }
    
    /**
     * 计算分数
     * @returns 分数
     */
    private calculateScore(): number {
        return this.currentWave * 1000 + this.playerGold;
    }
    
    /**
     * 更新UI
     */
    private updateUI(): void {
        Notifier.send(ListenID.DEFENSE_UI_UPDATE, {
            health: this.playerHealth,
            gold: this.playerGold,
            wave: this.currentWave,
            gameState: this.gameState
        });
    }
    
    /**
     * 清除所有防御塔
     */
    private clearAllTowers(): void {
        this.towers.forEach(tower => {
            NodePool.put("DefenseTower", tower);
        });
        this.towers = [];
    }
    
    /**
     * 清除所有敌人
     */
    private clearAllEnemies(): void {
        this.enemies.forEach(enemy => {
            NodePool.put("DefenseEnemy", enemy);
        });
        this.enemies = [];
    }
    
    /**
     * 获取游戏状态
     */
    getGameState(): string {
        return this.gameState;
    }
    
    /**
     * 获取当前波次
     */
    getCurrentWave(): number {
        return this.currentWave;
    }
    
    /**
     * 获取玩家生命值
     */
    getPlayerHealth(): number {
        return this.playerHealth;
    }
    
    /**
     * 获取玩家金币
     */
    getPlayerGold(): number {
        return this.playerGold;
    }
    
    /**
     * 销毁
     */
    destroy(): void {
        // 移除事件监听
        Notifier.off(ListenID.BUILD_TOWER, this.onBuildTower, this);
        Notifier.off(ListenID.UPGRADE_TOWER, this.onUpgradeTower, this);
        Notifier.off(ListenID.SELL_TOWER, this.onSellTower, this);
        Notifier.off(ListenID.START_WAVE, this.onStartWave, this);
        Notifier.off(ListenID.ENEMY_REACHED_END, this.onEnemyReachedEnd, this);
        Notifier.off(ListenID.ENEMY_KILLED, this.onEnemyKilled, this);
        
        // 清理资源
        this.clearAllTowers();
        this.clearAllEnemies();
        
        super.destroy();
    }
}

export { MDefense, TowerType };
