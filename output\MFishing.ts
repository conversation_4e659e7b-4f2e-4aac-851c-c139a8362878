// MFishing.ts
// 从 MFishing.js 转换而来

import { GameSeting } from "./GameSeting";
import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";

/**
 * 钓鱼游戏模式管理器
 */
export class MFishing extends GameSeting.CompBase {
    private fishingScene: any;
    private fishingRole: any;
    private fishingCannon: any;
    private fishes: any[] = [];
    private currentScore: number = 0;
    private gameTime: number = 0;
    
    constructor() {
        super();
        this.initializeFishing();
    }
    
    private initializeFishing(): void {
        this.setupEventListeners();
        this.resetGameState();
    }
    
    private setupEventListeners(): void {
        Notifier.on(ListenID.FISH_CAUGHT, this.onFishCaught, this);
        Notifier.on(ListenID.CANNON_FIRED, this.onCannonFired, this);
    }
    
    private resetGameState(): void {
        this.currentScore = 0;
        this.gameTime = 0;
        this.fishes = [];
    }
    
    private onFishCaught(data: any): void {
        const { fish, score } = data;
        this.currentScore += score;
        this.removeFish(fish);
    }
    
    private onCannonFired(data: any): void {
        const { position, power } = data;
        this.createNet(position, power);
    }
    
    private createNet(position: cc.Vec2, power: number): void {
        // 创建渔网逻辑
    }
    
    private removeFish(fish: any): void {
        const index = this.fishes.indexOf(fish);
        if (index !== -1) {
            this.fishes.splice(index, 1);
        }
    }
    
    update(dt: number): void {
        this.gameTime += dt;
        this.updateFishes(dt);
    }
    
    private updateFishes(dt: number): void {
        this.fishes.forEach(fish => {
            fish.update(dt);
        });
    }
    
    getCurrentScore(): number {
        return this.currentScore;
    }
    
    getGameTime(): number {
        return this.gameTime;
    }
}

export { MFishing };