// MGameMode.ts
// 从 MGameMode.js 转换而来

import { GameSeting } from "./GameSeting";

/**
 * 游戏模式枚举
 */
export enum GameModeType {
    STORY = "story",
    ENDLESS = "endless",
    CHALLENGE = "challenge",
    PVP = "pvp",
    COOP = "coop"
}

/**
 * 游戏模式基类
 */
export abstract class MGameMode extends GameSeting.CompBase {
    protected modeType: GameModeType;
    protected isActive: boolean = false;
    protected gameTime: number = 0;
    protected score: number = 0;
    
    constructor(modeType: GameModeType) {
        super();
        this.modeType = modeType;
    }
    
    /**
     * 开始游戏模式
     */
    startMode(): void {
        this.isActive = true;
        this.gameTime = 0;
        this.score = 0;
        this.onModeStart();
    }
    
    /**
     * 结束游戏模式
     */
    endMode(): void {
        this.isActive = false;
        this.onModeEnd();
    }
    
    /**
     * 暂停游戏模式
     */
    pauseMode(): void {
        this.isActive = false;
        this.onModePause();
    }
    
    /**
     * 恢复游戏模式
     */
    resumeMode(): void {
        this.isActive = true;
        this.onModeResume();
    }
    
    /**
     * 更新游戏模式
     */
    update(dt: number): void {
        if (this.isActive) {
            this.gameTime += dt;
            this.onModeUpdate(dt);
        }
    }
    
    /**
     * 模式开始时调用
     */
    protected abstract onModeStart(): void;
    
    /**
     * 模式结束时调用
     */
    protected abstract onModeEnd(): void;
    
    /**
     * 模式暂停时调用
     */
    protected abstract onModePause(): void;
    
    /**
     * 模式恢复时调用
     */
    protected abstract onModeResume(): void;
    
    /**
     * 模式更新时调用
     */
    protected abstract onModeUpdate(dt: number): void;
    
    /**
     * 获取模式类型
     */
    getModeType(): GameModeType {
        return this.modeType;
    }
    
    /**
     * 是否激活
     */
    isActivated(): boolean {
        return this.isActive;
    }
    
    /**
     * 获取游戏时间
     */
    getGameTime(): number {
        return this.gameTime;
    }
    
    /**
     * 获取分数
     */
    getScore(): number {
        return this.score;
    }
    
    /**
     * 添加分数
     */
    addScore(points: number): void {
        this.score += points;
    }
}

export { MGameMode, GameModeType };