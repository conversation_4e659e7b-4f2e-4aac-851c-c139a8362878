// LvOutsideCfg.ts
// 从 LvOutsideCfg.js 转换而来

import { TConfig } from "./TConfig";

/**
 * 关卡外部配置读取器
 */
export class LvOutsideCfgReader extends TConfig {
    protected _name: string = "LvOutside";
    
    constructor() {
        super();
    }
    
    /**
     * 获取关卡外部配置
     * @param levelId 关卡ID
     * @returns 关卡外部配置数据
     */
    getLevelOutsideConfig(levelId: number): any {
        return this.getConfig(levelId);
    }
    
    /**
     * 获取关卡基本信息
     * @param levelId 关卡ID
     * @returns 关卡基本信息
     */
    getLevelBasicInfo(levelId: number): any {
        const config = this.getLevelOutsideConfig(levelId);
        return config ? {
            name: config.name || `关卡${levelId}`,
            description: config.description || "",
            type: config.type || "normal",
            difficulty: config.difficulty || 1
        } : null;
    }
    
    /**
     * 获取关卡解锁条件
     * @param levelId 关卡ID
     * @returns 解锁条件
     */
    getUnlockConditions(levelId: number): any {
        const config = this.getLevelOutsideConfig(levelId);
        return config ? config.unlockConditions : null;
    }
    
    /**
     * 获取关卡奖励配置
     * @param levelId 关卡ID
     * @returns 奖励配置
     */
    getRewardConfig(levelId: number): any {
        const config = this.getLevelOutsideConfig(levelId);
        return config ? config.rewards : null;
    }
    
    /**
     * 获取关卡星级奖励
     * @param levelId 关卡ID
     * @param stars 星级数量
     * @returns 对应星级的奖励
     */
    getStarReward(levelId: number, stars: number): any {
        const rewardConfig = this.getRewardConfig(levelId);
        if (!rewardConfig || !rewardConfig.starRewards) {
            return null;
        }
        return rewardConfig.starRewards[stars] || null;
    }
    
    /**
     * 获取关卡首次通关奖励
     * @param levelId 关卡ID
     * @returns 首次通关奖励
     */
    getFirstClearReward(levelId: number): any {
        const rewardConfig = this.getRewardConfig(levelId);
        return rewardConfig ? rewardConfig.firstClear : null;
    }
    
    /**
     * 获取关卡完美通关奖励
     * @param levelId 关卡ID
     * @returns 完美通关奖励
     */
    getPerfectClearReward(levelId: number): any {
        const rewardConfig = this.getRewardConfig(levelId);
        return rewardConfig ? rewardConfig.perfectClear : null;
    }
    
    /**
     * 获取关卡消耗配置
     * @param levelId 关卡ID
     * @returns 消耗配置
     */
    getCostConfig(levelId: number): any {
        const config = this.getLevelOutsideConfig(levelId);
        return config ? config.cost : null;
    }
    
    /**
     * 获取关卡体力消耗
     * @param levelId 关卡ID
     * @returns 体力消耗值
     */
    getEnergyCost(levelId: number): number {
        const costConfig = this.getCostConfig(levelId);
        return costConfig ? costConfig.energy || 0 : 0;
    }
    
    /**
     * 获取关卡门票消耗
     * @param levelId 关卡ID
     * @returns 门票消耗配置
     */
    getTicketCost(levelId: number): any {
        const costConfig = this.getCostConfig(levelId);
        return costConfig ? costConfig.ticket : null;
    }
    
    /**
     * 获取关卡预览图
     * @param levelId 关卡ID
     * @returns 预览图路径
     */
    getPreviewImage(levelId: number): string {
        const config = this.getLevelOutsideConfig(levelId);
        return config ? config.previewImage || "" : "";
    }
    
    /**
     * 获取关卡背景音乐
     * @param levelId 关卡ID
     * @returns 背景音乐路径
     */
    getBackgroundMusic(levelId: number): string {
        const config = this.getLevelOutsideConfig(levelId);
        return config ? config.backgroundMusic || "" : "";
    }
    
    /**
     * 获取关卡标签
     * @param levelId 关卡ID
     * @returns 标签数组
     */
    getLevelTags(levelId: number): string[] {
        const config = this.getLevelOutsideConfig(levelId);
        return config ? config.tags || [] : [];
    }
    
    /**
     * 检查关卡是否为特殊关卡
     * @param levelId 关卡ID
     * @returns 是否为特殊关卡
     */
    isSpecialLevel(levelId: number): boolean {
        const tags = this.getLevelTags(levelId);
        return tags.includes("special") || tags.includes("boss") || tags.includes("event");
    }
    
    /**
     * 获取关卡推荐战力
     * @param levelId 关卡ID
     * @returns 推荐战力值
     */
    getRecommendedPower(levelId: number): number {
        const config = this.getLevelOutsideConfig(levelId);
        return config ? config.recommendedPower || 0 : 0;
    }
    
    /**
     * 获取关卡时间限制显示
     * @param levelId 关卡ID
     * @returns 时间限制显示文本
     */
    getTimeLimitDisplay(levelId: number): string {
        const config = this.getLevelOutsideConfig(levelId);
        if (!config || !config.timeLimit) {
            return "无限制";
        }
        
        const minutes = Math.floor(config.timeLimit / 60);
        const seconds = config.timeLimit % 60;
        
        if (minutes > 0) {
            return `${minutes}分${seconds}秒`;
        } else {
            return `${seconds}秒`;
        }
    }
    
    /**
     * 获取关卡评分标准
     * @param levelId 关卡ID
     * @returns 评分标准配置
     */
    getScoreStandards(levelId: number): any {
        const config = this.getLevelOutsideConfig(levelId);
        return config ? config.scoreStandards : null;
    }
    
    /**
     * 根据分数计算星级
     * @param levelId 关卡ID
     * @param score 得分
     * @returns 星级数量
     */
    calculateStars(levelId: number, score: number): number {
        const standards = this.getScoreStandards(levelId);
        if (!standards) {
            return 1; // 默认1星
        }
        
        if (score >= standards.threeStar) return 3;
        if (score >= standards.twoStar) return 2;
        if (score >= standards.oneStar) return 1;
        return 0;
    }
}

export { LvOutsideCfgReader };
