import { CallID } from "./CallID";
import { MVC } from "./MVC";
import { Notifier } from "./Notifier";
import { NotifyID } from "./NotifyID";
import { ListenID } from "./ListenID";
import { Time } from "./Time";
import { UIManager } from "./UIManager";
import { ModeThrowingKnifeModel } from "./ModeThrowingKnifeModel";

export class ModeThrowingKnifeController extends MVC.MController {
    private oldArgs: any;

    constructor() {
        super();
        this.setup(ModeThrowingKnifeModel.default.instance);
        this.changeListener(true);
    }

    public reset(): void {
        this._model.reset();
    }

    get classname(): string {
        return "ModeThrowingKnifeController";
    }

    public registerAllProtocol(): void {}

    public changeListener(isAdd: boolean): void {
        Notifier.changeListener(isAdd, ListenID.Game_Load, this.onOpenGame, this);
        Notifier.changeListener(isAdd, ListenID.Game_Replay, this.onReplay, this);
        Notifier.changeListener(isAdd, ListenID.Fight_BackToMain, this.backToMain, this);
    }

    public closeGame(): void {
        Notifier.send(ListenID.Fight_SetPause, true);
        UIManager.Close("ui/ModeTKnife/M30_FightUIView");
        UIManager.Close("ui/ModeTKnife/M30_FightScene");
    }

    public backToMain(): void {
        if (this._model.gameMode === this.cutMode) {
            this.closeGame();
        }
    }

    get cutMode(): any {
        return Notifier.call(CallID.Fight_GetCutMode);
    }

    public onOpenGame(gameMode: any, args: any): void {
        if (this._model.gameMode === gameMode) {
            this.oldArgs = args;
            args.setIsNeedLoading(false).setNodeGroup(0);
            UIManager.Open("ui/ModeTKnife/M30_FightScene", args);
        }
    }

    public onReplay(): void {
        if (this._model.gameMode === this.cutMode) {
            this.closeGame();
            Notifier.send(NotifyID.Game_LoadingView, true);
            Time.delay(1, () => {
                Notifier.send(NotifyID.Game_LoadingView, false);
                Notifier.send(ListenID.Game_Load, this.mode.gameMode, this.oldArgs);
            });
        }
    }
}
