var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Monster = undefined;
var $2ListenID = require("ListenID");
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2StateMachine = require("StateMachine");
var $2GameUtil = require("GameUtil");
var $2Game = require("Game");
var $2MonsterState = require("MonsterState");
var $2PropertyVo = require("PropertyVo");
var $2BaseEntity = require("BaseEntity");
var $2SteeringBehaviors = require("SteeringBehaviors");
var $2Vehicle = require("Vehicle");
var $2Manager = require("Manager");
var _ = cc.v2();
cc.v2();
var v = cc._decorator.ccclass;
var exp_Monster = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.removeTime = 0;
    t._monsterId = 0;
    t.lvCfg = null;
    t.arriveOnFirstPos = cc.v2();
    t.arriveOnSecondPos = cc.v2();
    t.colliderScaleSet = {
      w: .7,
      h: .7
    };
    t._countSort = 0;
    t._targetGap = 0;
    t._isOffScreen = false;
    t._OffNum = 0;
    t.curAttackDelta = 0;
    t.isHitBack = false;
    t._stateMachine = null;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "monsterId", {
    get: function () {
      return this._monsterId;
    },
    set: function (e) {
      var t = this._monsterId != e;
      this._monsterId = e;
      this.monCfg = $2Cfg.Cfg.Monster.get(e);
      if (t) {
        this.cleanAmClip();
        this.setAmClip();
      } else {
        this.playAction("move", true);
      }
      this.roleNode.setActive(true);
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "settingScale", {
    get: function () {
      return this.monCfg.Scale;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_MonsterEscape, this.onMonsterEscape, this);
  };
  _ctor.prototype.onMonsterEscape = function () {
    this.monCfg && 1 == this.monCfg.type && this.toEscape();
  };
  _ctor.prototype.setAmClip = function () {
    var e = this;
    if (this.monCfg.spine && this.mySkeleton) {
      this.mySkeleton.skeletonData = null;
      $2Manager.Manager.loader.loadSpine(this.monCfg.spine, this.game.gameNode).then(function (t) {
        e.mySkeleton.reset(t);
        e.Slot01 = e.mySkeleton.findSlot("slot01");
        e.Slot01 && (e.Slot01.color.a = 1);
        e.playAction("move", true);
        3 == e.monCfg.moveType && e.playAction("fiy");
        e.mySkeleton.setCompleteListener(function () {
          "attack" == e.mySkeleton.animation && e.playAction("idle", true);
        });
        e.delayByGame(function () {
          e.onNewSize(e.roleNode.getContentSize());
        });
      });
    }
  };
  _ctor.prototype.onNewSize = function (t) {
    var o;
    t.multiplySelf(this.colliderScaleSet.w, this.colliderScaleSet.h);
    this.node.setContentSize(t.width, t.height);
    this.collider.size = t;
    this.collider.offset = cc.v2(0, t.height / 2);
    this.radius = .5 * t.width;
    this._haedPosition.setVal(0, t.height * this.scale);
    this._bodyPosition.setVal(0, t.height * this.scale / 2);
    null === (o = this.skillMgr) || undefined === o || o.launchPoint.set(this._bodyPosition);
    e.prototype.onNewSize.call(this, t);
  };
  _ctor.prototype.init = function () {
    var t;
    var o;
    var i;
    var n;
    var r;
    var a = this;
    e.prototype.init.call(this);
    this.entityType = $2BaseEntity.EntityType.Monster;
    this.campType = $2BaseEntity.CampType.Two;
    this._OffNum = 0;
    this.roleNode.color = cc.Color.WHITE;
    this._steering || (this._steering = new $2SteeringBehaviors.default(this));
    this.twinkleNum = 0;
    null === (t = this._mals) || undefined === t || t.setProperty("setwhite", 0);
    this.setBaseProperty();
    this.initHp();
    this.registerState();
    null === (o = this.monCfg.buff) || undefined === o || o.forEach(function (e) {
      return a.addBuff(e);
    });
    null === (i = this.monCfg.skill) || undefined === i || i.forEach(function (e) {
      return a.addSkill(e);
    });
    this.checkNormalAtk();
    if (3 == this.monCfg.moveType) {
      null === (n = this._stateMachine) || undefined === n || n.changeState($2StateMachine.State.Type.APPEAR);
    } else {
      null === (r = this._stateMachine) || undefined === r || r.changeState($2StateMachine.State.Type.IDLE);
    }
  };
  _ctor.prototype.checkNormalAtk = function () {
    var e = this.monCfg.normalAtk;
    this.normalAtk = e;
    if (e) {
      var t = $2Game.ModeCfg.Skill.get(e);
      this.addSkill(e, false);
      this.property.base.atkArea = this.property.cut.atkArea = t.dis;
    }
  };
  _ctor.prototype.initHp = function () {
    var e;
    this.curHp = this.property.cut.hp;
    this.curArmor = this.lvCfg.sheild || 0;
    null === (e = this.roleNode) || undefined === e || e.setActive(true);
  };
  Object.defineProperty(_ctor.prototype, "targetGap", {
    get: function () {
      var e;
      return $2GameUtil.GameUtil.getDistance(this.node.position, null === (e = this.steering.targetAgent1) || undefined === e ? undefined : e.position);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.isOffScreen = function (e) {
    _ = this.node.position.sub(this.steering.targetAgent1.position);
    var t;
    var o = this.targetGap / 100;
    if ((t = Math.abs(_.x) > .7 * cc.view.getCanvasSize().width || Math.abs(_.y) > .7 * cc.view.getCanvasSize().height) && 1 == this._isOffScreen) {
      this._OffNum += e;
      this._OffNum > 25 - o && (this.isDead = true);
    } else {
      this._OffNum = 0;
    }
    return t;
  };
  _ctor.prototype.checkfarDead = function () {
    this.game.isVaild(this.position) || (this.isDead = true);
  };
  _ctor.prototype.readyToAttack = function () {
    return this.curAttackDelta >= this.property.cut.atkInterval;
  };
  _ctor.prototype.isInAttackRange = function () {
    if (!this.steering.targetAgent1) {
      return -1;
    }
    var e = cc.Vec2.squaredDistance(this.position, this.steering.targetAgent1.position);
    var t = this.property.cut.atkArea + this.radius + this.steering.targetAgent1.radius;
    var o = Math.pow(t, 2);
    var i = Math.pow(.4 * t, 2);
    if (e > o) {
      return -1;
    } else {
      if (e < i) {
        return 1;
      } else {
        return 0;
      }
    }
  };
  _ctor.prototype.toIdle = function () {
    this._stateMachine.isInState($2StateMachine.State.Type.DEAD) || this._stateMachine.changeState($2StateMachine.State.Type.IDLE);
  };
  _ctor.prototype.toMove = function () {
    this._stateMachine.isInState($2StateMachine.State.Type.DEAD) || this._stateMachine.changeState($2StateMachine.State.Type.MOVE);
  };
  _ctor.prototype.toAttack = function () {
    this._stateMachine.isInState($2StateMachine.State.Type.DEAD) || this._stateMachine.changeState($2StateMachine.State.Type.ATTACK);
  };
  _ctor.prototype.toDead = function (e) {
    var t;
    if (!this._stateMachine.isHasState([$2StateMachine.State.Type.DEAD])) {
      this._stateMachine.changeState($2StateMachine.State.Type.DEAD);
      null === (t = e.owner) || undefined === t || t.onKill(this.monCfg);
    }
  };
  _ctor.prototype.toBeHit = function (e) {
    undefined === e && (e = 0);
    this._stateMachine.isHasState([$2StateMachine.State.Type.SPRINT, $2StateMachine.State.Type.DEAD]) || this._stateMachine.changeState($2StateMachine.State.Type.BEHIT, e);
  };
  _ctor.prototype.toSprint = function (e) {
    this._stateMachine.changeState($2StateMachine.State.Type.SPRINT, e);
  };
  _ctor.prototype.toEscape = function () {
    this._stateMachine.changeState($2StateMachine.State.Type.ESCAPE);
  };
  _ctor.prototype.registerState = function () {
    if (!this._stateMachine) {
      this._stateMachine = new $2StateMachine.State.Machine(this);
      this._stateMachine.addState(new $2MonsterState.MonsterState.IdleState(this, false));
      this._stateMachine.addState(new $2MonsterState.MonsterState.MoveState(this, false));
      this._stateMachine.addState(new $2MonsterState.MonsterState.AttackState(this, true));
      this._stateMachine.addState(new $2MonsterState.MonsterState.DeadState(this, false));
      this._stateMachine.addState(new $2MonsterState.MonsterState.BeHit(this, false));
      this._stateMachine.addState(new $2MonsterState.MonsterState.Sprint(this, false));
      this._stateMachine.registerGlobalState(new $2MonsterState.MonsterState.GlobalState(this));
    }
  };
  _ctor.prototype.setBaseProperty = function () {
    this.property || (this.property = new $2PropertyVo.Property.Vo(this, this.lvCfg));
    this.property.base.hp = this.lvCfg.hp;
    this.property.base.speed = this.lvCfg.speed;
    this.property.base.atk = this.lvCfg.atk;
    this.property.base.atkArea = this.property.cut.atkArea = 10;
    if (this.game.isChallenge) {
      var e = this.game.curActivity;
      this.property.base.hp = Math.ceil(this.property.base.hp * (1 + ($2Manager.Manager.leveMgr.vo.curPassLv - e.unlockChapter) * e.diffValue));
      this.property.base.atk = Math.ceil(this.property.base.atk * (1 + ($2Manager.Manager.leveMgr.vo.curPassLv - e.unlockChapter) * e.diffValue));
    }
    this.updateProperty();
  };
  _ctor.prototype.updateProperty = function () {
    e.prototype.updateProperty.call(this);
  };
  _ctor.prototype.behit = function (e) {
    if (!this.isDead && this.hurtMgr.checkHurt(e)) {
      var t = e.val;
      var o = Math.min(this.curArmor, t);
      this.curArmor -= o;
      t -= o;
      this.curHp -= t;
      this.curArmor <= 0 && this.Slot01 && (this.Slot01.color.a = 0);
      e.isSlash || this.game.showDamageDisplay(e, this.haedPosition);
      this.materialTwinkle();
      this.node.emit($2ListenID.ListenID.Fight_BeHit, e);
      if (this.curHp <= 0) {
        this.toDead(e);
      } else {
        this.toBeHit(e.hitBack);
      }
      return e;
    }
  };
  _ctor.prototype.droppedItems = function () {
    var e = this;
    this.lvCfg.dropExpRatio && this.game.getDroppedItems(this.lvCfg.dropExpRatio).forEach(function (t) {
      e.game.knapsackMgr.addGoods(t.id, t.num);
    });
  };
  _ctor.prototype.materialTwinkle = function () {
    var e;
    this.twinkleNum = .6;
    null === (e = this._mals) || undefined === e || e.setProperty("setwhite", this.twinkleNum);
  };
  _ctor.prototype.onUpdate = function (t) {
    var o;
    this.isValid && this.twinkleNum > 0 && (null === (o = this._mals) || undefined === o || o.setProperty("setwhite", Math.max(0, this.twinkleNum -= .1)));
    e.prototype.onUpdate.call(this, t);
  };
  _ctor.prototype.enforceNonPeretration = function () {};
  _ctor.prototype.setTween = function () {
    $2Game.Game.tween(this.node).to(.5, {
      scaleX: .9,
      scaleY: 1.1
    }).to(.4, {
      scaleX: 1.1,
      scaleY: .9
    }).union().repeatForever().start();
  };
  return cc__decorate([v], _ctor);
}($2Vehicle.default);
exports.Monster = exp_Monster;