import { MVC } from "./MVC";
import { Game } from "./Game";

class ModePickUpBulletsModel extends MVC.BaseModel {
    private static _instance: ModePickUpBulletsModel = null;
    public gameMode: Game.Mode = Game.Mode.PICKUPBULLETS;

    constructor() {
        super();
        if (!ModePickUpBulletsModel._instance) {
            ModePickUpBulletsModel._instance = this;
        }
    }

    public reset(): void {}

    static get instance(): ModePickUpBulletsModel {
        if (!ModePickUpBulletsModel._instance) {
            ModePickUpBulletsModel._instance = new ModePickUpBulletsModel();
        }
        return ModePickUpBulletsModel._instance;
    }
}

export default ModePickUpBulletsModel;
