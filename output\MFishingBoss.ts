// MFishingBoss.ts
// 从 MFishingBoss.js 转换而来

import { MFishingFish } from "./MFishingFish";

/**
 * 钓鱼Boss鱼类
 */
export class MFishingBoss extends MFishingFish {
    private bossHealth: number = 1000;
    private maxHealth: number = 1000;
    private bossSkills: string[] = [];
    private skillCooldown: number = 0;
    
    constructor() {
        super();
        this.initBoss();
    }
    
    private initBoss(): void {
        this.bossSkills = ["lightning", "whirlpool", "rage"];
        this.skillCooldown = 0;
    }
    
    takeDamage(damage: number): void {
        this.bossHealth -= damage;
        if (this.bossHealth <= 0) {
            this.onBossDeath();
        }
    }
    
    private onBossDeath(): void {
        // Boss死亡逻辑
        this.dropSpecialReward();
    }
    
    private dropSpecialReward(): void {
        // 掉落特殊奖励
    }
    
    update(dt: number): void {
        super.update(dt);
        this.updateSkills(dt);
    }
    
    private updateSkills(dt: number): void {
        this.skillCooldown -= dt;
        if (this.skillCooldown <= 0) {
            this.useRandomSkill();
            this.skillCooldown = 5.0; // 5秒冷却
        }
    }
    
    private useRandomSkill(): void {
        const randomSkill = this.bossSkills[Math.floor(Math.random() * this.bossSkills.length)];
        this.executeSkill(randomSkill);
    }
    
    private executeSkill(skillName: string): void {
        switch (skillName) {
            case "lightning":
                this.lightningAttack();
                break;
            case "whirlpool":
                this.whirlpoolAttack();
                break;
            case "rage":
                this.rageMode();
                break;
        }
    }
    
    private lightningAttack(): void {
        // 闪电攻击
    }
    
    private whirlpoolAttack(): void {
        // 漩涡攻击
    }
    
    private rageMode(): void {
        // 愤怒模式
    }
    
    getHealthPercent(): number {
        return this.bossHealth / this.maxHealth;
    }
}

export { MFishingBoss };