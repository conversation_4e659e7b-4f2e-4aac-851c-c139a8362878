// MBackpackHero.ts
// 从 MBackpackHero.js 转换而来

import { CallID } from "./CallID";
import { ListenID } from "./ListenID";
import { Cfg } from "./Cfg";
import { CurrencyConfigCfg } from "./CurrencyConfigCfg";
import { GameSettingCfg } from "./GameSettingCfg";
import { GameatrCfg } from "./GameatrCfg";
import { Notifier } from "./Notifier";
import { Manager } from "./Manager";
import { Time } from "./Time";
import { GameUtil } from "./GameUtil";
import { KnapsackVo } from "./KnapsackVo";
import { RecordVo } from "./RecordVo";
import { Game } from "./Game";
import { BronMonsterManger } from "./BronMonsterManger";
import { BaseEntity } from "./BaseEntity";
import { MonsterTidalBoss } from "./MonsterTidalBoss";
import { CompManager } from "./CompManager";
import { NodePool } from "./NodePool";
import { ModeBackpackHeroModel } from "./ModeBackpackHeroModel";
import { M20Prop } from "./M20Prop";
import { M20Prop_Equip } from "./M20Prop_Equip";
import { BackHeroProp } from "./BackHeroProp";
import { BackpackHeroHome } from "./BackpackHeroHome";

/**
 * 回合状态枚举
 */
export enum RoundStatus {
    NONE = 0,
    BATTLE = 1,
    ONTICK = 2,
    SELECTEQUIP = 3,
    END = 4
}

/**
 * 道具类型枚举
 */
export enum PropType {
    MergeEquipment = 1,
    Block = 2,
    Gem = 3
}

/**
 * 游戏模式枚举
 */
export enum GameMode {
    Default = 1,
    Challenge = 2
}

/**
 * 背包英雄模式管理器
 */
export class MBPack extends GameSeting.CompBase {
    /** 当前回合状态 */
    private currentRoundStatus: RoundStatus = RoundStatus.NONE;

    /** 当前游戏模式 */
    private currentGameMode: GameMode = GameMode.Default;

    /** 背包网格大小 */
    private bagGridSize: { width: number; height: number } = { width: 6, height: 8 };

    /** 背包道具列表 */
    private bagItems: M20Prop[] = [];

    /** 装备道具列表 */
    private equipItems: M20Prop_Equip[] = [];

    /** 当前选中的道具 */
    private selectedProp: M20Prop | null = null;

    /** 怪物管理器 */
    private monsterManager: BronMonsterManger;

    /** 游戏数据模型 */
    private gameModel: ModeBackpackHeroModel;

    /** 背包英雄主界面 */
    private heroHome: BackpackHeroHome;

    /** 当前关卡 */
    private currentLevel: number = 1;

    /** 当前波次 */
    private currentWave: number = 1;

    /** 玩家血量 */
    private playerHealth: number = 100;

    /** 最大血量 */
    private maxHealth: number = 100;

    /** 护甲值 */
    private armor: number = 0;

    /** 攻击力 */
    private attackPower: number = 10;

    /** 金币数量 */
    private goldAmount: number = 0;

    /** 经验值 */
    private experience: number = 0;

    /** 等级 */
    private playerLevel: number = 1;

    /** 是否游戏暂停 */
    private isPaused: boolean = false;

    /** 战斗计时器 */
    private battleTimer: number = 0;

    /** 回合计数器 */
    private roundCounter: number = 0;

    constructor() {
        super();
        this.initializeGame();
    }

    /**
     * 初始化游戏
     */
    private initializeGame(): void {
        this.gameModel = Manager.getModel(ModeBackpackHeroModel);
        this.monsterManager = new BronMonsterManger();
        this.heroHome = new BackpackHeroHome();

        this.setupEventListeners();
        this.initializeBag();
        this.resetGameState();
    }

    /**
     * 设置事件监听
     */
    private setupEventListeners(): void {
        Notifier.on(ListenID.PROP_SELECTED, this.onPropSelected, this);
        Notifier.on(ListenID.PROP_MERGED, this.onPropMerged, this);
        Notifier.on(ListenID.EQUIP_EQUIPPED, this.onEquipEquipped, this);
        Notifier.on(ListenID.BATTLE_START, this.onBattleStart, this);
        Notifier.on(ListenID.BATTLE_END, this.onBattleEnd, this);
        Notifier.on(ListenID.MONSTER_DEFEATED, this.onMonsterDefeated, this);
        Notifier.on(ListenID.PLAYER_DAMAGED, this.onPlayerDamaged, this);
        Notifier.on(ListenID.ROUND_COMPLETE, this.onRoundComplete, this);
    }

    /**
     * 初始化背包
     */
    private initializeBag(): void {
        // 清空背包
        this.bagItems = [];

        // 添加初始道具
        this.addInitialItems();
    }

    /**
     * 添加初始道具
     */
    private addInitialItems(): void {
        // 添加基础装备
        const basicSword = this.createProp(PropType.MergeEquipment, 1001);
        const basicShield = this.createProp(PropType.Block, 2001);

        if (basicSword) this.addPropToBag(basicSword);
        if (basicShield) this.addPropToBag(basicShield);
    }

    /**
     * 创建道具
     * @param type 道具类型
     * @param configId 配置ID
     * @returns 道具实例
     */
    private createProp(type: PropType, configId: number): M20Prop | null {
        try {
            let prop: M20Prop;

            switch (type) {
                case PropType.MergeEquipment:
                    prop = NodePool.get("M20Prop_Equip") as M20Prop_Equip;
                    break;
                case PropType.Block:
                    prop = NodePool.get("M20Prop") as M20Prop;
                    break;
                case PropType.Gem:
                    prop = NodePool.get("M20Prop") as M20Prop;
                    break;
                default:
                    return null;
            }

            if (prop) {
                prop.initWithConfig(configId);
                return prop;
            }
        } catch (error) {
            console.error("创建道具失败:", error);
        }

        return null;
    }

    /**
     * 添加道具到背包
     * @param prop 道具实例
     * @returns 是否成功添加
     */
    addPropToBag(prop: M20Prop): boolean {
        if (this.bagItems.length >= this.bagGridSize.width * this.bagGridSize.height) {
            console.warn("背包已满，无法添加道具");
            return false;
        }

        this.bagItems.push(prop);
        this.updateBagUI();
        return true;
    }

    /**
     * 从背包移除道具
     * @param prop 道具实例
     */
    removePropFromBag(prop: M20Prop): void {
        const index = this.bagItems.indexOf(prop);
        if (index !== -1) {
            this.bagItems.splice(index, 1);
            this.updateBagUI();
        }
    }

    /**
     * 更新背包UI
     */
    private updateBagUI(): void {
        Notifier.send(ListenID.BAG_UPDATED, {
            items: this.bagItems,
            gridSize: this.bagGridSize
        });
    }

    /**
     * 道具选中事件
     * @param data 事件数据
     */
    private onPropSelected(data: any): void {
        const { prop } = data;
        this.selectedProp = prop;

        // 高亮显示选中的道具
        this.highlightSelectedProp(prop);
    }

    /**
     * 高亮选中的道具
     * @param prop 道具实例
     */
    private highlightSelectedProp(prop: M20Prop): void {
        // 清除之前的高亮
        this.bagItems.forEach(item => {
            item.setHighlight(false);
        });

        // 设置当前选中道具高亮
        if (prop) {
            prop.setHighlight(true);
        }
    }

    /**
     * 道具合并事件
     * @param data 事件数据
     */
    private onPropMerged(data: any): void {
        const { sourceProp, targetProp, resultProp } = data;

        // 移除原道具
        this.removePropFromBag(sourceProp);
        this.removePropFromBag(targetProp);

        // 添加合并后的道具
        if (resultProp) {
            this.addPropToBag(resultProp);
        }

        // 记录合并事件
        RecordVo.record("prop_merged", {
            sourceId: sourceProp.getConfigId(),
            targetId: targetProp.getConfigId(),
            resultId: resultProp ? resultProp.getConfigId() : null,
            timestamp: Date.now()
        });
    }

    /**
     * 装备穿戴事件
     * @param data 事件数据
     */
    private onEquipEquipped(data: any): void {
        const { equip } = data;

        if (equip instanceof M20Prop_Equip) {
            this.equipItems.push(equip);
            this.updatePlayerStats();

            // 从背包中移除
            this.removePropFromBag(equip);
        }
    }

    /**
     * 更新玩家属性
     */
    private updatePlayerStats(): void {
        let totalAttack = this.attackPower;
        let totalArmor = this.armor;
        let totalHealth = this.maxHealth;

        // 计算装备加成
        this.equipItems.forEach(equip => {
            const stats = equip.getStats();
            totalAttack += stats.attack || 0;
            totalArmor += stats.armor || 0;
            totalHealth += stats.health || 0;
        });

        this.attackPower = totalAttack;
        this.armor = totalArmor;
        this.maxHealth = totalHealth;

        // 更新UI显示
        this.updatePlayerStatsUI();
    }

    /**
     * 更新玩家属性UI
     */
    private updatePlayerStatsUI(): void {
        Notifier.send(ListenID.PLAYER_STATS_UPDATED, {
            health: this.playerHealth,
            maxHealth: this.maxHealth,
            armor: this.armor,
            attack: this.attackPower,
            level: this.playerLevel,
            experience: this.experience,
            gold: this.goldAmount
        });
    }

    /**
     * 战斗开始事件
     */
    private onBattleStart(): void {
        this.currentRoundStatus = RoundStatus.BATTLE;
        this.battleTimer = 0;
        this.isPaused = false;

        // 生成怪物
        this.spawnMonsters();

        // 开始战斗循环
        this.startBattleLoop();
    }

    /**
     * 生成怪物
     */
    private spawnMonsters(): void {
        const monsterConfigs = this.gameModel.getCurrentWaveMonsters(this.currentWave);

        monsterConfigs.forEach((config: any) => {
            const monster = this.monsterManager.createMonster(config);
            if (monster) {
                this.monsterManager.addMonster(monster);
            }
        });
    }

    /**
     * 开始战斗循环
     */
    private startBattleLoop(): void {
        const battleLoop = () => {
            if (this.currentRoundStatus !== RoundStatus.BATTLE || this.isPaused) {
                return;
            }

            this.battleTimer += Time.deltaTime;
            this.updateBattle();

            // 继续循环
            requestAnimationFrame(battleLoop);
        };

        battleLoop();
    }

    /**
     * 更新战斗
     */
    private updateBattle(): void {
        // 更新怪物
        this.monsterManager.update();

        // 检查战斗结束条件
        if (this.monsterManager.getAllMonsters().length === 0) {
            this.onBattleEnd();
        } else if (this.playerHealth <= 0) {
            this.onGameOver();
        }
    }

    /**
     * 战斗结束事件
     */
    private onBattleEnd(): void {
        this.currentRoundStatus = RoundStatus.SELECTEQUIP;

        // 给予奖励
        this.giveRewards();

        // 进入选择装备阶段
        this.enterEquipSelectionPhase();
    }

    /**
     * 给予奖励
     */
    private giveRewards(): void {
        const waveConfig = this.gameModel.getWaveConfig(this.currentWave);

        if (waveConfig && waveConfig.rewards) {
            // 金币奖励
            if (waveConfig.rewards.gold) {
                this.goldAmount += waveConfig.rewards.gold;
            }

            // 经验奖励
            if (waveConfig.rewards.exp) {
                this.addExperience(waveConfig.rewards.exp);
            }

            // 道具奖励
            if (waveConfig.rewards.items) {
                waveConfig.rewards.items.forEach((itemConfig: any) => {
                    const prop = this.createProp(itemConfig.type, itemConfig.id);
                    if (prop) {
                        this.addPropToBag(prop);
                    }
                });
            }
        }

        this.updatePlayerStatsUI();
    }

    /**
     * 添加经验值
     * @param exp 经验值
     */
    private addExperience(exp: number): void {
        this.experience += exp;

        // 检查升级
        const expNeeded = this.getExpNeededForLevel(this.playerLevel + 1);
        if (this.experience >= expNeeded) {
            this.levelUp();
        }
    }

    /**
     * 获取升级所需经验
     * @param level 目标等级
     * @returns 所需经验值
     */
    private getExpNeededForLevel(level: number): number {
        return level * 100; // 简单的经验计算公式
    }

    /**
     * 升级
     */
    private levelUp(): void {
        this.playerLevel++;
        this.experience = 0; // 重置经验值

        // 提升基础属性
        this.maxHealth += 10;
        this.playerHealth = this.maxHealth; // 满血
        this.attackPower += 2;

        // 播放升级特效
        CompManager.playEffect("level_up", this.heroHome.getPosition());

        // 发送升级通知
        Notifier.send(ListenID.PLAYER_LEVEL_UP, {
            newLevel: this.playerLevel,
            healthBonus: 10,
            attackBonus: 2
        });

        this.updatePlayerStatsUI();
    }
}