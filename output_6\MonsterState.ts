import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";
import { StateMachine } from "./StateMachine";
import { GameUtil } from "./GameUtil";
import { Game } from "./Game";

const tempVec = cc.v2();

export namespace MonsterState {
    export class MonsterIdleState extends StateMachine.State.BaseModel {
        private static _instance: MonsterIdleState;

        static get instance(): MonsterIdleState {
            if (!MonsterIdleState._instance) {
                MonsterIdleState._instance = new MonsterIdleState();
            }
            return MonsterIdleState._instance;
        }

        get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.IDLE;
        }

        public onEnter(monster: any): void {
            super.onEnter(monster);
            monster.playAction("idle", true);
        }

        public onUpdate(monster: any, dt: number): void {
            super.onUpdate(monster, dt);
            if (monster.isInAttackRange()) {
                if (monster.canAttack()) {
                    this._parentState.changeState(StateMachine.State.Type.ATTACK);
                }
                tempVec.set(monster.position);
                monster.enforceNonPeretration(tempVec);
                monster.setPosition(tempVec);
            } else {
                this._parentState.changeState(StateMachine.State.Type.MOVE);
            }
        }

        public onExit(monster: any): void {
            super.onExit(monster);
        }
    }

    export class MonsterMoveState extends StateMachine.State.BaseModel {
        private static _instance: MonsterMoveState;

        static get instance(): MonsterMoveState {
            if (!MonsterMoveState._instance) {
                MonsterMoveState._instance = new MonsterMoveState();
            }
            return MonsterMoveState._instance;
        }

        get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.MOVE;
        }

        public onEnter(monster: any): void {
            super.onEnter(monster);
            monster.playAction("move", true);
        }

        public onUpdate(monster: any, dt: number): void {
            super.onUpdate(monster, dt);
            
            if (monster.steering) {
                const steeringForce = monster.steering.calculate();
                monster.velocity.addSelf(steeringForce.mul(dt));
                monster.velocity = monster.velocity.normalize().mul(monster.maxSpeed);
                
                tempVec.set(monster.position);
                tempVec.addSelf(monster.velocity.mul(dt));
                monster.enforceNonPeretration(tempVec);
                monster.setPosition(tempVec);
            }

            if (monster.isInAttackRange()) {
                this._parentState.changeState(StateMachine.State.Type.IDLE);
            }
        }

        public onExit(monster: any): void {
            super.onExit(monster);
        }
    }

    export class MonsterAttackState extends StateMachine.State.BaseModel {
        private static _instance: MonsterAttackState;

        static get instance(): MonsterAttackState {
            if (!MonsterAttackState._instance) {
                MonsterAttackState._instance = new MonsterAttackState();
            }
            return MonsterAttackState._instance;
        }

        get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.ATTACK;
        }

        public onEnter(monster: any): void {
            super.onEnter(monster);
            monster.playAction("attack", false);
            monster.resetAttackDelta();
        }

        public onUpdate(monster: any, dt: number): void {
            super.onUpdate(monster, dt);
            
            if (!monster.mySkeleton?.isPlaying) {
                this._parentState.changeState(StateMachine.State.Type.IDLE);
            }
        }

        public onExit(monster: any): void {
            super.onExit(monster);
        }
    }

    export class MonsterDeadState extends StateMachine.State.BaseModel {
        private static _instance: MonsterDeadState;

        static get instance(): MonsterDeadState {
            if (!MonsterDeadState._instance) {
                MonsterDeadState._instance = new MonsterDeadState();
            }
            return MonsterDeadState._instance;
        }

        get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.DEAD;
        }

        public onEnter(monster: any): void {
            super.onEnter(monster);
            monster.playAction("dead", false);
            monster.isDead = true;
            
            // Notify death
            Notifier.send(ListenID.Monster_Dead, monster);
            
            // Schedule removal
            monster.scheduleOnce(() => {
                monster.removeFromGame();
            }, 2);
        }

        public onUpdate(monster: any, dt: number): void {
            super.onUpdate(monster, dt);
        }

        public onExit(monster: any): void {
            super.onExit(monster);
        }
    }

    export class MonsterGlobalState extends StateMachine.State.BaseModel {
        private static _instance: MonsterGlobalState;

        static get instance(): MonsterGlobalState {
            if (!MonsterGlobalState._instance) {
                MonsterGlobalState._instance = new MonsterGlobalState();
            }
            return MonsterGlobalState._instance;
        }

        get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.GLOBAL;
        }

        public onEnter(monster: any): void {
            super.onEnter(monster);
        }

        public onUpdate(monster: any, dt: number): void {
            super.onUpdate(monster, dt);
            
            // Check if monster is off screen
            if (monster.isOffScreen) {
                monster.removeFromGame();
            }
            
            // Check if monster should be removed due to time
            monster.removeTime += dt;
            if (monster.removeTime > 30) { // 30 seconds timeout
                monster.removeFromGame();
            }
        }

        public onExit(monster: any): void {
            super.onExit(monster);
        }
    }
}
