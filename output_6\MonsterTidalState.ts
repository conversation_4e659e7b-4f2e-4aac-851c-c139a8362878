import { StateMachine } from "./StateMachine";
import { GameUtil } from "./GameUtil";

const tempVec = cc.v2();

export namespace MonsterTidalState {
    export class MonsterTidalMoveState extends StateMachine.State.BaseModel {
        private static _instance: MonsterTidalMoveState;
        public direction: number = 1;
        public distanceTraveledHorizontally: number = 0;
        public range: number = 120;
        public dtMoveX: number = 0.5;
        public bAligning: boolean = false;

        static get instance(): MonsterTidalMoveState {
            if (!MonsterTidalMoveState._instance) {
                MonsterTidalMoveState._instance = new MonsterTidalMoveState();
            }
            return MonsterTidalMoveState._instance;
        }

        get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.MOVE;
        }

        public onEnter(monster: any): void {
            super.onEnter(monster);
            monster.playAction("move", true);
            this.direction = Math.random() > 0.5 ? 1 : -1;
            this.distanceTraveledHorizontally = 0;
            this.bAligning = false;
        }

        public onUpdate(monster: any, dt: number): void {
            super.onUpdate(monster, dt);
            
            if (!this.bAligning) {
                // Horizontal movement
                const horizontalSpeed = monster.maxSpeed * this.dtMoveX;
                const horizontalDistance = horizontalSpeed * dt * this.direction;
                this.distanceTraveledHorizontally += Math.abs(horizontalDistance);
                
                if (this.distanceTraveledHorizontally >= this.range) {
                    this.direction *= -1;
                    this.distanceTraveledHorizontally = 0;
                }
                
                tempVec.set(monster.position);
                tempVec.x += horizontalDistance;
                tempVec.y -= monster.maxSpeed * dt; // Move down
                
                monster.enforceNonPeretration(tempVec);
                monster.setPosition(tempVec);
            } else {
                // Vertical movement only
                tempVec.set(monster.position);
                tempVec.y -= monster.maxSpeed * dt;
                monster.setPosition(tempVec);
            }
            
            if (monster.isInAttackRange()) {
                this._parentState.changeState(StateMachine.State.Type.IDLE);
            }
        }

        public onExit(monster: any): void {
            super.onExit(monster);
        }
    }

    export class MonsterTidalIdleState extends StateMachine.State.BaseModel {
        private static _instance: MonsterTidalIdleState;

        static get instance(): MonsterTidalIdleState {
            if (!MonsterTidalIdleState._instance) {
                MonsterTidalIdleState._instance = new MonsterTidalIdleState();
            }
            return MonsterTidalIdleState._instance;
        }

        get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.IDLE;
        }

        public onEnter(monster: any): void {
            super.onEnter(monster);
            monster.playAction("idle", true);
        }

        public onUpdate(monster: any, dt: number): void {
            super.onUpdate(monster, dt);
            
            if (monster.isInAttackRange()) {
                if (monster.canAttack()) {
                    this._parentState.changeState(StateMachine.State.Type.ATTACK);
                }
            } else {
                this._parentState.changeState(StateMachine.State.Type.MOVE);
            }
        }

        public onExit(monster: any): void {
            super.onExit(monster);
        }
    }

    export class MonsterTidalAttackState extends StateMachine.State.BaseModel {
        private static _instance: MonsterTidalAttackState;

        static get instance(): MonsterTidalAttackState {
            if (!MonsterTidalAttackState._instance) {
                MonsterTidalAttackState._instance = new MonsterTidalAttackState();
            }
            return MonsterTidalAttackState._instance;
        }

        get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.ATTACK;
        }

        public onEnter(monster: any): void {
            super.onEnter(monster);
            monster.playAction("attack", false);
            monster.resetAttackDelta();
        }

        public onUpdate(monster: any, dt: number): void {
            super.onUpdate(monster, dt);
            
            if (!monster.mySkeleton?.isPlaying) {
                this._parentState.changeState(StateMachine.State.Type.IDLE);
            }
        }

        public onExit(monster: any): void {
            super.onExit(monster);
        }
    }

    export class MonsterTidalGlobalState extends StateMachine.State.BaseModel {
        private static _instance: MonsterTidalGlobalState;

        static get instance(): MonsterTidalGlobalState {
            if (!MonsterTidalGlobalState._instance) {
                MonsterTidalGlobalState._instance = new MonsterTidalGlobalState();
            }
            return MonsterTidalGlobalState._instance;
        }

        get tag(): StateMachine.State.Type {
            return StateMachine.State.Type.GLOBAL;
        }

        public onEnter(monster: any): void {
            super.onEnter(monster);
        }

        public onUpdate(monster: any, dt: number): void {
            super.onUpdate(monster, dt);
            
            // Check if monster should be removed
            if (monster.position.y < -1000) {
                monster.removeFromGame();
            }
        }

        public onExit(monster: any): void {
            super.onExit(monster);
        }
    }
}
