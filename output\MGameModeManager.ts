// MGameModeManager.ts
// 从 MGameModeManager.js 转换而来

import { MGameMode, GameModeType } from "./MGameMode";
import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";

/**
 * 游戏模式管理器
 */
export class MGameModeManager {
    private static _instance: MGameModeManager = null;
    private gameModes: Map<GameModeType, MGameMode> = new Map();
    private currentMode: MGameMode | null = null;
    
    public static get instance(): MGameModeManager {
        if (!this._instance) {
            this._instance = new MGameModeManager();
        }
        return this._instance;
    }
    
    constructor() {
        this.setupEventListeners();
    }
    
    private setupEventListeners(): void {
        Notifier.on(ListenID.CHANGE_GAME_MODE, this.onChangeGameMode, this);
        Notifier.on(ListenID.PAUSE_GAME_MODE, this.onPauseGameMode, this);
        Notifier.on(ListenID.RESUME_GAME_MODE, this.onResumeGameMode, this);
    }
    
    /**
     * 注册游戏模式
     */
    registerGameMode(mode: MGameMode): void {
        this.gameModes.set(mode.getModeType(), mode);
    }
    
    /**
     * 切换游戏模式
     */
    switchToMode(modeType: GameModeType): boolean {
        const mode = this.gameModes.get(modeType);
        if (!mode) {
            console.error(`游戏模式不存在: ${modeType}`);
            return false;
        }
        
        // 结束当前模式
        if (this.currentMode) {
            this.currentMode.endMode();
        }
        
        // 切换到新模式
        this.currentMode = mode;
        this.currentMode.startMode();
        
        // 发送模式切换通知
        Notifier.send(ListenID.GAME_MODE_CHANGED, {
            oldMode: this.currentMode ? this.currentMode.getModeType() : null,
            newMode: modeType
        });
        
        return true;
    }
    
    /**
     * 获取当前游戏模式
     */
    getCurrentMode(): MGameMode | null {
        return this.currentMode;
    }
    
    /**
     * 获取当前模式类型
     */
    getCurrentModeType(): GameModeType | null {
        return this.currentMode ? this.currentMode.getModeType() : null;
    }
    
    /**
     * 更新当前模式
     */
    update(dt: number): void {
        if (this.currentMode) {
            this.currentMode.update(dt);
        }
    }
    
    /**
     * 暂停当前模式
     */
    pauseCurrentMode(): void {
        if (this.currentMode) {
            this.currentMode.pauseMode();
        }
    }
    
    /**
     * 恢复当前模式
     */
    resumeCurrentMode(): void {
        if (this.currentMode) {
            this.currentMode.resumeMode();
        }
    }
    
    /**
     * 切换游戏模式事件
     */
    private onChangeGameMode(data: any): void {
        const { modeType } = data;
        this.switchToMode(modeType);
    }
    
    /**
     * 暂停游戏模式事件
     */
    private onPauseGameMode(): void {
        this.pauseCurrentMode();
    }
    
    /**
     * 恢复游戏模式事件
     */
    private onResumeGameMode(): void {
        this.resumeCurrentMode();
    }
    
    /**
     * 检查模式是否存在
     */
    hasModeType(modeType: GameModeType): boolean {
        return this.gameModes.has(modeType);
    }
    
    /**
     * 获取所有注册的模式类型
     */
    getAllModeTypes(): GameModeType[] {
        return Array.from(this.gameModes.keys());
    }
    
    /**
     * 销毁
     */
    destroy(): void {
        // 结束当前模式
        if (this.currentMode) {
            this.currentMode.endMode();
        }
        
        // 清理所有模式
        this.gameModes.clear();
        this.currentMode = null;
        
        // 移除事件监听
        Notifier.off(ListenID.CHANGE_GAME_MODE, this.onChangeGameMode, this);
        Notifier.off(ListenID.PAUSE_GAME_MODE, this.onPauseGameMode, this);
        Notifier.off(ListenID.RESUME_GAME_MODE, this.onResumeGameMode, this);
    }
}

export { MGameModeManager };