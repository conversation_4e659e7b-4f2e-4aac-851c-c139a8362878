// MChains.ts
// 从 MChains.js 转换而来

import { GameSeting } from "./GameSeting";
import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { NodePool } from "./NodePool";
import { CompManager } from "./CompManager";

/**
 * 链条游戏模式管理器
 */
export class MChains extends GameSeting.CompBase {
    /** 链条节点列表 */
    private chainNodes: cc.Node[] = [];
    
    /** 链条连接点 */
    private connectionPoints: cc.Vec2[] = [];
    
    /** 当前选中的节点 */
    private selectedNode: cc.Node | null = null;
    
    /** 链条材质 */
    private chainMaterial: cc.Material;
    
    /** 链条宽度 */
    private chainWidth: number = 10;
    
    /** 最大链条长度 */
    private maxChainLength: number = 500;
    
    /** 链条强度 */
    private chainStrength: number = 100;
    
    /** 是否正在拖拽 */
    private isDragging: boolean = false;
    
    /** 拖拽起始点 */
    private dragStartPoint: cc.Vec2 = cc.Vec2.ZERO;
    
    /** 当前拖拽点 */
    private currentDragPoint: cc.Vec2 = cc.Vec2.ZERO;
    
    constructor() {
        super();
        this.initializeChains();
        this.setupEventListeners();
    }
    
    /**
     * 初始化链条系统
     */
    private initializeChains(): void {
        this.chainNodes = [];
        this.connectionPoints = [];
        this.loadChainMaterial();
    }
    
    /**
     * 加载链条材质
     */
    private loadChainMaterial(): void {
        Manager.loader.loadMaterial("chain_material").then((material: cc.Material) => {
            this.chainMaterial = material;
        });
    }
    
    /**
     * 设置事件监听
     */
    private setupEventListeners(): void {
        Notifier.on(ListenID.CHAIN_NODE_SELECTED, this.onChainNodeSelected, this);
        Notifier.on(ListenID.CHAIN_DRAG_START, this.onChainDragStart, this);
        Notifier.on(ListenID.CHAIN_DRAG_MOVE, this.onChainDragMove, this);
        Notifier.on(ListenID.CHAIN_DRAG_END, this.onChainDragEnd, this);
        Notifier.on(ListenID.CHAIN_BREAK, this.onChainBreak, this);
    }
    
    /**
     * 创建链条节点
     * @param position 位置
     * @param type 节点类型
     * @returns 链条节点
     */
    createChainNode(position: cc.Vec2, type: string = "normal"): cc.Node {
        const node = NodePool.get("ChainNode");
        if (node) {
            node.setPosition(position);
            node.getComponent("ChainNodeComponent").setType(type);
            this.chainNodes.push(node);
            return node;
        }
        return null;
    }
    
    /**
     * 连接两个节点
     * @param nodeA 节点A
     * @param nodeB 节点B
     * @returns 是否连接成功
     */
    connectNodes(nodeA: cc.Node, nodeB: cc.Node): boolean {
        if (!nodeA || !nodeB || nodeA === nodeB) {
            return false;
        }
        
        const distance = cc.Vec2.distance(nodeA.position, nodeB.position);
        if (distance > this.maxChainLength) {
            console.warn("链条长度超过最大限制");
            return false;
        }
        
        // 创建链条连接
        const chain = this.createChainConnection(nodeA, nodeB);
        if (chain) {
            this.addConnectionPoint(nodeA.position);
            this.addConnectionPoint(nodeB.position);
            
            // 发送连接成功通知
            Notifier.send(ListenID.CHAIN_CONNECTED, {
                nodeA: nodeA,
                nodeB: nodeB,
                chain: chain
            });
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 创建链条连接
     * @param nodeA 节点A
     * @param nodeB 节点B
     * @returns 链条对象
     */
    private createChainConnection(nodeA: cc.Node, nodeB: cc.Node): any {
        const chainObject = {
            nodeA: nodeA,
            nodeB: nodeB,
            strength: this.chainStrength,
            length: cc.Vec2.distance(nodeA.position, nodeB.position),
            graphics: this.createChainGraphics(nodeA.position, nodeB.position)
        };
        
        return chainObject;
    }
    
    /**
     * 创建链条图形
     * @param startPos 起始位置
     * @param endPos 结束位置
     * @returns 图形组件
     */
    private createChainGraphics(startPos: cc.Vec2, endPos: cc.Vec2): cc.Graphics {
        const node = new cc.Node("ChainGraphics");
        const graphics = node.addComponent(cc.Graphics);
        
        graphics.lineWidth = this.chainWidth;
        graphics.strokeColor = cc.Color.GRAY;
        
        // 绘制链条
        this.drawChain(graphics, startPos, endPos);
        
        return graphics;
    }
    
    /**
     * 绘制链条
     * @param graphics 图形组件
     * @param startPos 起始位置
     * @param endPos 结束位置
     */
    private drawChain(graphics: cc.Graphics, startPos: cc.Vec2, endPos: cc.Vec2): void {
        graphics.clear();
        
        // 计算链条的弯曲
        const midPoint = startPos.add(endPos).mul(0.5);
        const distance = cc.Vec2.distance(startPos, endPos);
        const sag = distance * 0.1; // 下垂程度
        
        const controlPoint = cc.v2(midPoint.x, midPoint.y - sag);
        
        // 绘制贝塞尔曲线
        graphics.moveTo(startPos.x, startPos.y);
        graphics.quadraticCurveTo(controlPoint.x, controlPoint.y, endPos.x, endPos.y);
        graphics.stroke();
        
        // 绘制链环
        this.drawChainLinks(graphics, startPos, endPos, controlPoint);
    }
    
    /**
     * 绘制链环
     * @param graphics 图形组件
     * @param startPos 起始位置
     * @param endPos 结束位置
     * @param controlPoint 控制点
     */
    private drawChainLinks(graphics: cc.Graphics, startPos: cc.Vec2, endPos: cc.Vec2, controlPoint: cc.Vec2): void {
        const linkCount = Math.floor(cc.Vec2.distance(startPos, endPos) / 20);
        
        for (let i = 1; i < linkCount; i++) {
            const t = i / linkCount;
            const pos = this.getBezierPoint(startPos, controlPoint, endPos, t);
            
            // 绘制链环
            graphics.circle(pos.x, pos.y, 3);
            graphics.fill();
        }
    }
    
    /**
     * 获取贝塞尔曲线上的点
     * @param p0 起始点
     * @param p1 控制点
     * @param p2 结束点
     * @param t 参数
     * @returns 曲线上的点
     */
    private getBezierPoint(p0: cc.Vec2, p1: cc.Vec2, p2: cc.Vec2, t: number): cc.Vec2 {
        const u = 1 - t;
        const tt = t * t;
        const uu = u * u;
        
        const x = uu * p0.x + 2 * u * t * p1.x + tt * p2.x;
        const y = uu * p0.y + 2 * u * t * p1.y + tt * p2.y;
        
        return cc.v2(x, y);
    }
    
    /**
     * 添加连接点
     * @param point 连接点位置
     */
    private addConnectionPoint(point: cc.Vec2): void {
        this.connectionPoints.push(point);
    }
    
    /**
     * 链条节点选中事件
     * @param data 事件数据
     */
    private onChainNodeSelected(data: any): void {
        const { node } = data;
        this.selectedNode = node;
        
        // 高亮显示选中的节点
        this.highlightNode(node);
    }
    
    /**
     * 高亮节点
     * @param node 节点
     */
    private highlightNode(node: cc.Node): void {
        // 清除之前的高亮
        this.chainNodes.forEach(n => {
            n.opacity = 255;
        });
        
        // 高亮当前节点
        if (node) {
            node.opacity = 180;
        }
    }
    
    /**
     * 链条拖拽开始事件
     * @param data 事件数据
     */
    private onChainDragStart(data: any): void {
        const { position } = data;
        this.isDragging = true;
        this.dragStartPoint = position;
        this.currentDragPoint = position;
    }
    
    /**
     * 链条拖拽移动事件
     * @param data 事件数据
     */
    private onChainDragMove(data: any): void {
        const { position } = data;
        if (this.isDragging) {
            this.currentDragPoint = position;
            this.updateDragPreview();
        }
    }
    
    /**
     * 更新拖拽预览
     */
    private updateDragPreview(): void {
        // 显示拖拽预览线
        Notifier.send(ListenID.CHAIN_DRAG_PREVIEW, {
            startPos: this.dragStartPoint,
            endPos: this.currentDragPoint
        });
    }
    
    /**
     * 链条拖拽结束事件
     * @param data 事件数据
     */
    private onChainDragEnd(data: any): void {
        const { position, targetNode } = data;
        this.isDragging = false;
        
        if (this.selectedNode && targetNode && this.selectedNode !== targetNode) {
            this.connectNodes(this.selectedNode, targetNode);
        }
        
        // 清除拖拽预览
        Notifier.send(ListenID.CHAIN_DRAG_PREVIEW_CLEAR);
    }
    
    /**
     * 链条断裂事件
     * @param data 事件数据
     */
    private onChainBreak(data: any): void {
        const { chain } = data;
        this.breakChain(chain);
    }
    
    /**
     * 断裂链条
     * @param chain 链条对象
     */
    private breakChain(chain: any): void {
        if (chain && chain.graphics) {
            // 播放断裂特效
            CompManager.playEffect("chain_break", chain.graphics.node.position);
            
            // 移除图形
            chain.graphics.node.destroy();
            
            // 发送断裂通知
            Notifier.send(ListenID.CHAIN_BROKEN, { chain: chain });
        }
    }
    
    /**
     * 获取所有链条节点
     * @returns 链条节点数组
     */
    getAllChainNodes(): cc.Node[] {
        return [...this.chainNodes];
    }
    
    /**
     * 获取连接点数量
     * @returns 连接点数量
     */
    getConnectionCount(): number {
        return this.connectionPoints.length;
    }
    
    /**
     * 清除所有链条
     */
    clearAllChains(): void {
        this.chainNodes.forEach(node => {
            NodePool.put("ChainNode", node);
        });
        
        this.chainNodes = [];
        this.connectionPoints = [];
        this.selectedNode = null;
    }
    
    /**
     * 销毁
     */
    destroy(): void {
        // 移除事件监听
        Notifier.off(ListenID.CHAIN_NODE_SELECTED, this.onChainNodeSelected, this);
        Notifier.off(ListenID.CHAIN_DRAG_START, this.onChainDragStart, this);
        Notifier.off(ListenID.CHAIN_DRAG_MOVE, this.onChainDragMove, this);
        Notifier.off(ListenID.CHAIN_DRAG_END, this.onChainDragEnd, this);
        Notifier.off(ListenID.CHAIN_BREAK, this.onChainBreak, this);
        
        // 清理资源
        this.clearAllChains();
        
        super.destroy();
    }
}

export { MChains };
