import { ListenID } from "./ListenID";
import { Cfg } from "./Cfg";
import { Notifier } from "./Notifier";
import { StateMachine } from "./StateMachine";
import { GameUtil } from "./GameUtil";
import { Game } from "./Game";
import { MonsterState } from "./MonsterState";
import { PropertyVo } from "./PropertyVo";
import { BaseEntity } from "./BaseEntity";
import { SteeringBehaviors } from "./SteeringBehaviors";
import { Vehicle } from "./Vehicle";
import { Manager } from "./Manager";

const { ccclass } = cc._decorator;

@ccclass
export class Monster extends Vehicle {
    public removeTime: number = 0;
    private _monsterId: number = 0;
    public lvCfg: any = null;
    public arriveOnFirstPos: cc.Vec2 = cc.v2();
    public arriveOnSecondPos: cc.Vec2 = cc.v2();
    public colliderScaleSet: any = { w: 0.7, h: 0.7 };
    private _countSort: number = 0;
    private _targetGap: number = 0;
    private _isOffScreen: boolean = false;
    private _OffNum: number = 0;
    public curAttackDelta: number = 0;
    public isHitBack: boolean = false;
    private _stateMachine: StateMachine = null;

    get monsterId(): number {
        return this._monsterId;
    }

    set monsterId(id: number) {
        const isChanged = this._monsterId !== id;
        this._monsterId = id;
        if (isChanged) {
            this.monCfg = Cfg.Monster.get(id);
            this.setAmClip();
        }
    }

    get monCfg(): any {
        return this._monCfg;
    }

    set monCfg(config: any) {
        this._monCfg = config;
        if (config) {
            this.entityType = BaseEntity.EntityType.Monster;
            this.property = new PropertyVo.Property.Vo(this, {
                speed: config.speed,
                atk: config.atk,
                hp: config.hp
            });
        }
    }

    get stateMachine(): StateMachine {
        return this._stateMachine;
    }

    set stateMachine(machine: StateMachine) {
        this._stateMachine = machine;
    }

    get countSort(): number {
        return this._countSort;
    }

    set countSort(value: number) {
        this._countSort = value;
    }

    get targetGap(): number {
        return this._targetGap;
    }

    set targetGap(gap: number) {
        this._targetGap = gap;
    }

    get isOffScreen(): boolean {
        return this._isOffScreen;
    }

    set isOffScreen(value: boolean) {
        this._isOffScreen = value;
        if (value) {
            this._OffNum++;
        }
    }

    get OffNum(): number {
        return this._OffNum;
    }

    public init(): void {
        super.init();
        this.stateMachine = new StateMachine(this);
        this.stateMachine.setCurrentState(MonsterState.MonsterGlobalState.instance);
        this.stateMachine.setGlobalState(MonsterState.MonsterGlobalState.instance);
        this.stateMachine.changeState(MonsterState.MonsterIdleState.instance);
    }

    public setAmClip(): void {
        if (this.monCfg && this.monCfg.spine) {
            Manager.loader.loadSpineNode(this.monCfg.spine, {
                nodeAttr: {
                    parent: this.node,
                    position: cc.Vec2.ZERO
                },
                spAttr: {
                    loop: true,
                    defaultAnim: "idle",
                    isPlayerOnLoad: true
                }
            }).then((skeleton: any) => {
                this.mySkeleton = skeleton;
                this.roleNode = skeleton.node;
                this.setColliderScale();
            });
        }
    }

    public setColliderScale(): void {
        if (this.collider && this.colliderScaleSet) {
            this.collider.size = cc.size(
                this.collider.size.width * this.colliderScaleSet.w,
                this.collider.size.height * this.colliderScaleSet.h
            );
        }
    }

    public onUpdate(dt: number): void {
        super.onUpdate(dt);
        this.stateMachine?.update();
        this.updateAttackDelta(dt);
    }

    public updateAttackDelta(dt: number): void {
        this.curAttackDelta += dt;
    }

    public toMove(): void {
        this.stateMachine?.changeState(MonsterState.MonsterMoveState.instance);
    }

    public toAttack(): void {
        this.stateMachine?.changeState(MonsterState.MonsterAttackState.instance);
    }

    public toIdle(): void {
        this.stateMachine?.changeState(MonsterState.MonsterIdleState.instance);
    }

    public toDead(): void {
        this.stateMachine?.changeState(MonsterState.MonsterDeadState.instance);
        super.toDead();
    }

    public onCollisionEnter(collision: any): void {
        super.onCollisionEnter(collision);
        if (collision.comp?.entityType === BaseEntity.EntityType.Bullet) {
            this.onHitByBullet(collision.comp);
        }
    }

    public onHitByBullet(bullet: any): void {
        const damage = bullet.vo?.hurt?.baseVal || 0;
        this.takeDamage(damage);
        
        if (bullet.vo?.hitBack) {
            this.applyHitBack(bullet);
        }
    }

    public takeDamage(damage: number): void {
        this.property.hp.curVal -= damage;
        if (this.property.hp.curVal <= 0) {
            this.toDead();
        }
    }

    public applyHitBack(bullet: any): void {
        this.isHitBack = true;
        const hitBackForce = bullet.vo.hitBack || 100;
        const direction = bullet.velocity.normalize();
        this.velocity.addSelf(direction.mul(hitBackForce));
        
        Game.tween(this).delay(0.2).call(() => {
            this.isHitBack = false;
        }).start();
    }

    public aiFire(): void {
        if (this.canAttack()) {
            this.toAttack();
        }
    }

    public canAttack(): boolean {
        return this.curAttackDelta >= (this.monCfg?.attackInterval || 1);
    }

    public resetAttackDelta(): void {
        this.curAttackDelta = 0;
    }

    public getDistanceToTarget(): number {
        if (this.steering?.target) {
            return cc.Vec2.distance(this.position, this.steering.target.position);
        }
        return Infinity;
    }

    public isInAttackRange(): boolean {
        const attackRange = this.monCfg?.attackRange || 100;
        return this.getDistanceToTarget() <= attackRange;
    }

    public playAction(animName: string, loop: boolean = false): void {
        if (this.mySkeleton) {
            this.mySkeleton.setAnimation(0, animName, loop);
        }
    }

    public removeFromGame(): void {
        this.removeEntityToUpdate();
        this.node.removeFromParent();
        Notifier.send(ListenID.Monster_Removed, this);
    }
}
