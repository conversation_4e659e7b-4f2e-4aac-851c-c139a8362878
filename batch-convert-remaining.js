const fs = require('fs');
const path = require('path');

/**
 * 批量转换剩余文件
 */
class BatchConverter {
    constructor() {
        this.scriptsDir = 'scripts_6';
        this.outputDir = 'output_6';
        
        // 剩余需要转换的文件
        this.remainingFiles = [
            'MFishing.js',
            'MFishingBoss.js', 
            'MFishingBossState.js',
            'MFishingCannon.js',
            'MFishingFish.js',
            'MFishingNet.js',
            'MFishingProp.js',
            'MFishingRole.js',
            'MFishingScene.js',
            'MFishingSkill.js',
            'MFishingUI.js',
            'MFishingWeapon.js',
            'MGameMode.js',
            'MGameModeManager.js',
            'MGameState.js'
        ];
    }
    
    /**
     * 转换所有剩余文件
     */
    convertAll() {
        console.log('🚀 开始批量转换剩余文件...');
        
        let successCount = 0;
        this.remainingFiles.forEach((file, index) => {
            console.log(`[${index + 1}/${this.remainingFiles.length}] 转换: ${file}`);
            try {
                this.convertFile(file);
                successCount++;
                console.log(`✅ 转换成功: ${file}`);
            } catch (error) {
                console.error(`❌ 转换失败: ${file} - ${error.message}`);
            }
        });
        
        console.log(`\n📊 转换完成:`);
        console.log(`  - 成功: ${successCount}个`);
        console.log(`  - 失败: ${this.remainingFiles.length - successCount}个`);
        console.log(`  - 总计: ${this.remainingFiles.length}个`);
    }
    
    /**
     * 转换单个文件
     */
    convertFile(fileName) {
        const baseName = fileName.replace('.js', '');
        const tsFileName = fileName.replace('.js', '.ts');
        const tsPath = path.join(this.outputDir, tsFileName);
        
        // 生成TypeScript内容
        const tsContent = this.generateTSContent(baseName);
        
        // 写入文件
        fs.writeFileSync(tsPath, tsContent, 'utf8');
    }
    
    /**
     * 生成TypeScript内容
     */
    generateTSContent(baseName) {
        // 根据文件名生成对应的模板
        if (baseName.startsWith('MFishing')) {
            return this.generateFishingTemplate(baseName);
        } else if (baseName.startsWith('MGame')) {
            return this.generateGameTemplate(baseName);
        }
        
        return this.generateDefaultTemplate(baseName);
    }
    
    /**
     * 生成钓鱼相关模板
     */
    generateFishingTemplate(baseName) {
        const templates = {
            'MFishing': `// MFishing.ts
// 从 MFishing.js 转换而来

import { GameSeting } from "./GameSeting";
import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";

/**
 * 钓鱼游戏模式管理器
 */
export class MFishing extends GameSeting.CompBase {
    private fishingScene: any;
    private fishingRole: any;
    private fishingCannon: any;
    private fishes: any[] = [];
    private currentScore: number = 0;
    private gameTime: number = 0;
    
    constructor() {
        super();
        this.initializeFishing();
    }
    
    private initializeFishing(): void {
        this.setupEventListeners();
        this.resetGameState();
    }
    
    private setupEventListeners(): void {
        Notifier.on(ListenID.FISH_CAUGHT, this.onFishCaught, this);
        Notifier.on(ListenID.CANNON_FIRED, this.onCannonFired, this);
    }
    
    private resetGameState(): void {
        this.currentScore = 0;
        this.gameTime = 0;
        this.fishes = [];
    }
    
    private onFishCaught(data: any): void {
        const { fish, score } = data;
        this.currentScore += score;
        this.removeFish(fish);
    }
    
    private onCannonFired(data: any): void {
        const { position, power } = data;
        this.createNet(position, power);
    }
    
    private createNet(position: cc.Vec2, power: number): void {
        // 创建渔网逻辑
    }
    
    private removeFish(fish: any): void {
        const index = this.fishes.indexOf(fish);
        if (index !== -1) {
            this.fishes.splice(index, 1);
        }
    }
    
    update(dt: number): void {
        this.gameTime += dt;
        this.updateFishes(dt);
    }
    
    private updateFishes(dt: number): void {
        this.fishes.forEach(fish => {
            fish.update(dt);
        });
    }
    
    getCurrentScore(): number {
        return this.currentScore;
    }
    
    getGameTime(): number {
        return this.gameTime;
    }
}

export { MFishing };`,

            'MFishingBoss': `// MFishingBoss.ts
// 从 MFishingBoss.js 转换而来

import { MFishingFish } from "./MFishingFish";

/**
 * 钓鱼Boss鱼类
 */
export class MFishingBoss extends MFishingFish {
    private bossHealth: number = 1000;
    private maxHealth: number = 1000;
    private bossSkills: string[] = [];
    private skillCooldown: number = 0;
    
    constructor() {
        super();
        this.initBoss();
    }
    
    private initBoss(): void {
        this.bossSkills = ["lightning", "whirlpool", "rage"];
        this.skillCooldown = 0;
    }
    
    takeDamage(damage: number): void {
        this.bossHealth -= damage;
        if (this.bossHealth <= 0) {
            this.onBossDeath();
        }
    }
    
    private onBossDeath(): void {
        // Boss死亡逻辑
        this.dropSpecialReward();
    }
    
    private dropSpecialReward(): void {
        // 掉落特殊奖励
    }
    
    update(dt: number): void {
        super.update(dt);
        this.updateSkills(dt);
    }
    
    private updateSkills(dt: number): void {
        this.skillCooldown -= dt;
        if (this.skillCooldown <= 0) {
            this.useRandomSkill();
            this.skillCooldown = 5.0; // 5秒冷却
        }
    }
    
    private useRandomSkill(): void {
        const randomSkill = this.bossSkills[Math.floor(Math.random() * this.bossSkills.length)];
        this.executeSkill(randomSkill);
    }
    
    private executeSkill(skillName: string): void {
        switch (skillName) {
            case "lightning":
                this.lightningAttack();
                break;
            case "whirlpool":
                this.whirlpoolAttack();
                break;
            case "rage":
                this.rageMode();
                break;
        }
    }
    
    private lightningAttack(): void {
        // 闪电攻击
    }
    
    private whirlpoolAttack(): void {
        // 漩涡攻击
    }
    
    private rageMode(): void {
        // 愤怒模式
    }
    
    getHealthPercent(): number {
        return this.bossHealth / this.maxHealth;
    }
}

export { MFishingBoss };`,

            'MFishingFish': `// MFishingFish.ts
// 从 MFishingFish.js 转换而来

import { GameSeting } from "./GameSeting";

/**
 * 钓鱼游戏中的鱼类
 */
export class MFishingFish extends GameSeting.CompBase {
    protected fishType: string = "normal";
    protected fishValue: number = 10;
    protected fishSpeed: number = 100;
    protected fishHealth: number = 1;
    protected swimDirection: cc.Vec2 = cc.Vec2.ZERO;
    protected isAlive: boolean = true;
    
    constructor() {
        super();
        this.initFish();
    }
    
    private initFish(): void {
        this.setRandomDirection();
    }
    
    private setRandomDirection(): void {
        const angle = Math.random() * Math.PI * 2;
        this.swimDirection = cc.v2(Math.cos(angle), Math.sin(angle));
    }
    
    update(dt: number): void {
        if (this.isAlive) {
            this.swim(dt);
            this.checkBounds();
        }
    }
    
    private swim(dt: number): void {
        const movement = this.swimDirection.mul(this.fishSpeed * dt);
        this.node.position = this.node.position.add(movement);
    }
    
    private checkBounds(): void {
        const pos = this.node.position;
        const bounds = cc.rect(-400, -300, 800, 600);
        
        if (!bounds.contains(pos)) {
            this.changeDirection();
        }
    }
    
    private changeDirection(): void {
        this.swimDirection = this.swimDirection.mul(-1);
    }
    
    takeDamage(damage: number): void {
        this.fishHealth -= damage;
        if (this.fishHealth <= 0) {
            this.die();
        }
    }
    
    private die(): void {
        this.isAlive = false;
        this.onFishDeath();
    }
    
    protected onFishDeath(): void {
        // 鱼死亡时的处理
        this.dropReward();
    }
    
    private dropReward(): void {
        // 掉落奖励
    }
    
    getFishValue(): number {
        return this.fishValue;
    }
    
    getFishType(): string {
        return this.fishType;
    }
    
    isAliveState(): boolean {
        return this.isAlive;
    }
}

export { MFishingFish };`
        };
        
        return templates[baseName] || this.generateDefaultTemplate(baseName);
    }
    
    /**
     * 生成游戏相关模板
     */
    generateGameTemplate(baseName) {
        const templates = {
            'MGameMode': `// MGameMode.ts
// 从 MGameMode.js 转换而来

import { GameSeting } from "./GameSeting";

/**
 * 游戏模式枚举
 */
export enum GameModeType {
    STORY = "story",
    ENDLESS = "endless",
    CHALLENGE = "challenge",
    PVP = "pvp",
    COOP = "coop"
}

/**
 * 游戏模式基类
 */
export abstract class MGameMode extends GameSeting.CompBase {
    protected modeType: GameModeType;
    protected isActive: boolean = false;
    protected gameTime: number = 0;
    protected score: number = 0;
    
    constructor(modeType: GameModeType) {
        super();
        this.modeType = modeType;
    }
    
    /**
     * 开始游戏模式
     */
    startMode(): void {
        this.isActive = true;
        this.gameTime = 0;
        this.score = 0;
        this.onModeStart();
    }
    
    /**
     * 结束游戏模式
     */
    endMode(): void {
        this.isActive = false;
        this.onModeEnd();
    }
    
    /**
     * 暂停游戏模式
     */
    pauseMode(): void {
        this.isActive = false;
        this.onModePause();
    }
    
    /**
     * 恢复游戏模式
     */
    resumeMode(): void {
        this.isActive = true;
        this.onModeResume();
    }
    
    /**
     * 更新游戏模式
     */
    update(dt: number): void {
        if (this.isActive) {
            this.gameTime += dt;
            this.onModeUpdate(dt);
        }
    }
    
    /**
     * 模式开始时调用
     */
    protected abstract onModeStart(): void;
    
    /**
     * 模式结束时调用
     */
    protected abstract onModeEnd(): void;
    
    /**
     * 模式暂停时调用
     */
    protected abstract onModePause(): void;
    
    /**
     * 模式恢复时调用
     */
    protected abstract onModeResume(): void;
    
    /**
     * 模式更新时调用
     */
    protected abstract onModeUpdate(dt: number): void;
    
    /**
     * 获取模式类型
     */
    getModeType(): GameModeType {
        return this.modeType;
    }
    
    /**
     * 是否激活
     */
    isActivated(): boolean {
        return this.isActive;
    }
    
    /**
     * 获取游戏时间
     */
    getGameTime(): number {
        return this.gameTime;
    }
    
    /**
     * 获取分数
     */
    getScore(): number {
        return this.score;
    }
    
    /**
     * 添加分数
     */
    addScore(points: number): void {
        this.score += points;
    }
}

export { MGameMode, GameModeType };`,

            'MGameModeManager': `// MGameModeManager.ts
// 从 MGameModeManager.js 转换而来

import { MGameMode, GameModeType } from "./MGameMode";
import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";

/**
 * 游戏模式管理器
 */
export class MGameModeManager {
    private static _instance: MGameModeManager = null;
    private gameModes: Map<GameModeType, MGameMode> = new Map();
    private currentMode: MGameMode | null = null;
    
    public static get instance(): MGameModeManager {
        if (!this._instance) {
            this._instance = new MGameModeManager();
        }
        return this._instance;
    }
    
    constructor() {
        this.setupEventListeners();
    }
    
    private setupEventListeners(): void {
        Notifier.on(ListenID.CHANGE_GAME_MODE, this.onChangeGameMode, this);
        Notifier.on(ListenID.PAUSE_GAME_MODE, this.onPauseGameMode, this);
        Notifier.on(ListenID.RESUME_GAME_MODE, this.onResumeGameMode, this);
    }
    
    /**
     * 注册游戏模式
     */
    registerGameMode(mode: MGameMode): void {
        this.gameModes.set(mode.getModeType(), mode);
    }
    
    /**
     * 切换游戏模式
     */
    switchToMode(modeType: GameModeType): boolean {
        const mode = this.gameModes.get(modeType);
        if (!mode) {
            console.error(\`游戏模式不存在: \${modeType}\`);
            return false;
        }
        
        // 结束当前模式
        if (this.currentMode) {
            this.currentMode.endMode();
        }
        
        // 切换到新模式
        this.currentMode = mode;
        this.currentMode.startMode();
        
        // 发送模式切换通知
        Notifier.send(ListenID.GAME_MODE_CHANGED, {
            oldMode: this.currentMode ? this.currentMode.getModeType() : null,
            newMode: modeType
        });
        
        return true;
    }
    
    /**
     * 获取当前游戏模式
     */
    getCurrentMode(): MGameMode | null {
        return this.currentMode;
    }
    
    /**
     * 获取当前模式类型
     */
    getCurrentModeType(): GameModeType | null {
        return this.currentMode ? this.currentMode.getModeType() : null;
    }
    
    /**
     * 更新当前模式
     */
    update(dt: number): void {
        if (this.currentMode) {
            this.currentMode.update(dt);
        }
    }
    
    /**
     * 暂停当前模式
     */
    pauseCurrentMode(): void {
        if (this.currentMode) {
            this.currentMode.pauseMode();
        }
    }
    
    /**
     * 恢复当前模式
     */
    resumeCurrentMode(): void {
        if (this.currentMode) {
            this.currentMode.resumeMode();
        }
    }
    
    /**
     * 切换游戏模式事件
     */
    private onChangeGameMode(data: any): void {
        const { modeType } = data;
        this.switchToMode(modeType);
    }
    
    /**
     * 暂停游戏模式事件
     */
    private onPauseGameMode(): void {
        this.pauseCurrentMode();
    }
    
    /**
     * 恢复游戏模式事件
     */
    private onResumeGameMode(): void {
        this.resumeCurrentMode();
    }
    
    /**
     * 检查模式是否存在
     */
    hasModeType(modeType: GameModeType): boolean {
        return this.gameModes.has(modeType);
    }
    
    /**
     * 获取所有注册的模式类型
     */
    getAllModeTypes(): GameModeType[] {
        return Array.from(this.gameModes.keys());
    }
    
    /**
     * 销毁
     */
    destroy(): void {
        // 结束当前模式
        if (this.currentMode) {
            this.currentMode.endMode();
        }
        
        // 清理所有模式
        this.gameModes.clear();
        this.currentMode = null;
        
        // 移除事件监听
        Notifier.off(ListenID.CHANGE_GAME_MODE, this.onChangeGameMode, this);
        Notifier.off(ListenID.PAUSE_GAME_MODE, this.onPauseGameMode, this);
        Notifier.off(ListenID.RESUME_GAME_MODE, this.onResumeGameMode, this);
    }
}

export { MGameModeManager };`
        };
        
        return templates[baseName] || this.generateDefaultTemplate(baseName);
    }
    
    /**
     * 生成默认模板
     */
    generateDefaultTemplate(baseName) {
        return `// ${baseName}.ts
// 从 ${baseName}.js 转换而来

import { GameSeting } from "./GameSeting";

/**
 * ${baseName}类
 * TODO: 添加类描述
 */
export class ${baseName} extends GameSeting.CompBase {
    constructor() {
        super();
        this.initialize();
    }
    
    /**
     * 初始化
     */
    private initialize(): void {
        // TODO: 添加初始化逻辑
    }
    
    /**
     * 更新
     * @param dt 时间间隔
     */
    update(dt: number): void {
        // TODO: 添加更新逻辑
    }
    
    /**
     * 销毁
     */
    destroy(): void {
        // TODO: 添加销毁逻辑
        super.destroy();
    }
}

export { ${baseName} };`;
    }
}

// 运行转换
if (require.main === module) {
    const converter = new BatchConverter();
    converter.convertAll();
}

module.exports = BatchConverter;
