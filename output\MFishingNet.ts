// MFishingNet.ts
// 从 MFishingNet.js 转换而来

import { GameSeting } from "./GameSeting";

/**
 * MFishingNet类
 * TODO: 添加类描述
 */
export class MFishingNet extends GameSeting.CompBase {
    constructor() {
        super();
        this.initialize();
    }
    
    /**
     * 初始化
     */
    private initialize(): void {
        // TODO: 添加初始化逻辑
    }
    
    /**
     * 更新
     * @param dt 时间间隔
     */
    update(dt: number): void {
        // TODO: 添加更新逻辑
    }
    
    /**
     * 销毁
     */
    destroy(): void {
        // TODO: 添加销毁逻辑
        super.destroy();
    }
}

export { MFishingNet };