// MFishingFish.ts
// 从 MFishingFish.js 转换而来

import { GameSeting } from "./GameSeting";

/**
 * 钓鱼游戏中的鱼类
 */
export class MFishingFish extends GameSeting.CompBase {
    protected fishType: string = "normal";
    protected fishValue: number = 10;
    protected fishSpeed: number = 100;
    protected fishHealth: number = 1;
    protected swimDirection: cc.Vec2 = cc.Vec2.ZERO;
    protected isAlive: boolean = true;
    
    constructor() {
        super();
        this.initFish();
    }
    
    private initFish(): void {
        this.setRandomDirection();
    }
    
    private setRandomDirection(): void {
        const angle = Math.random() * Math.PI * 2;
        this.swimDirection = cc.v2(Math.cos(angle), Math.sin(angle));
    }
    
    update(dt: number): void {
        if (this.isAlive) {
            this.swim(dt);
            this.checkBounds();
        }
    }
    
    private swim(dt: number): void {
        const movement = this.swimDirection.mul(this.fishSpeed * dt);
        this.node.position = this.node.position.add(movement);
    }
    
    private checkBounds(): void {
        const pos = this.node.position;
        const bounds = cc.rect(-400, -300, 800, 600);
        
        if (!bounds.contains(pos)) {
            this.changeDirection();
        }
    }
    
    private changeDirection(): void {
        this.swimDirection = this.swimDirection.mul(-1);
    }
    
    takeDamage(damage: number): void {
        this.fishHealth -= damage;
        if (this.fishHealth <= 0) {
            this.die();
        }
    }
    
    private die(): void {
        this.isAlive = false;
        this.onFishDeath();
    }
    
    protected onFishDeath(): void {
        // 鱼死亡时的处理
        this.dropReward();
    }
    
    private dropReward(): void {
        // 掉落奖励
    }
    
    getFishValue(): number {
        return this.fishValue;
    }
    
    getFishType(): string {
        return this.fishType;
    }
    
    isAliveState(): boolean {
        return this.isAlive;
    }
}

export { MFishingFish };