// LocalStorage.ts
// 从 LocalStorage.js 转换而来

import StorageSync from "./StorageSync";

/**
 * 本地存储类
 * 提供统一的本地存储接口，自动适配不同环境
 */
export default class LocalStorage {
    
    /**
     * 获取存储项
     * @param key 存储键
     * @returns 存储值
     */
    static getItem(key: string): string | null {
        if (typeof window === 'undefined' || window.localStorage === undefined) {
            return StorageSync.getItem(key);
        } else {
            return window.localStorage.getItem(key);
        }
    }
    
    /**
     * 设置存储项
     * @param key 存储键
     * @param value 存储值
     */
    static setItem(key: string, value: string): void {
        if (typeof window === 'undefined' || window.localStorage === undefined) {
            StorageSync.setItem(key, value);
        } else {
            window.localStorage.setItem(key, value);
        }
    }
    
    /**
     * 移除存储项
     * @param key 存储键
     */
    static removeItem(key: string): void {
        if (typeof window === 'undefined' || window.localStorage === undefined) {
            StorageSync.removeItem(key);
        } else {
            window.localStorage.removeItem(key);
        }
    }
    
    /**
     * 清空所有存储
     */
    static clear(): void {
        if (typeof window === 'undefined' || window.localStorage === undefined) {
            StorageSync.clear();
        } else {
            window.localStorage.clear();
        }
    }
    
    /**
     * 获取存储项数量
     * @returns 存储项数量
     */
    static length(): number {
        if (typeof window === 'undefined' || window.localStorage === undefined) {
            return StorageSync.length();
        } else {
            return window.localStorage.length;
        }
    }
    
    /**
     * 根据索引获取键名
     * @param index 索引
     * @returns 键名
     */
    static key(index: number): string | null {
        if (typeof window === 'undefined' || window.localStorage === undefined) {
            return StorageSync.key(index);
        } else {
            return window.localStorage.key(index);
        }
    }
    
    /**
     * 检查是否支持localStorage
     * @returns 是否支持
     */
    static isSupported(): boolean {
        try {
            return typeof window !== 'undefined' && 
                   window.localStorage !== undefined &&
                   window.localStorage !== null;
        } catch (e) {
            return false;
        }
    }
    
    /**
     * 获取JSON对象
     * @param key 存储键
     * @param defaultValue 默认值
     * @returns 解析后的对象
     */
    static getJSON<T>(key: string, defaultValue?: T): T | null {
        try {
            const value = this.getItem(key);
            if (value === null) {
                return defaultValue || null;
            }
            return JSON.parse(value);
        } catch (e) {
            console.error(`解析JSON失败: ${key}`, e);
            return defaultValue || null;
        }
    }
    
    /**
     * 设置JSON对象
     * @param key 存储键
     * @param value 要存储的对象
     */
    static setJSON(key: string, value: any): void {
        try {
            const jsonString = JSON.stringify(value);
            this.setItem(key, jsonString);
        } catch (e) {
            console.error(`序列化JSON失败: ${key}`, e);
        }
    }
    
    /**
     * 获取数字值
     * @param key 存储键
     * @param defaultValue 默认值
     * @returns 数字值
     */
    static getNumber(key: string, defaultValue: number = 0): number {
        const value = this.getItem(key);
        if (value === null) {
            return defaultValue;
        }
        const num = parseFloat(value);
        return isNaN(num) ? defaultValue : num;
    }
    
    /**
     * 设置数字值
     * @param key 存储键
     * @param value 数字值
     */
    static setNumber(key: string, value: number): void {
        this.setItem(key, value.toString());
    }
    
    /**
     * 获取布尔值
     * @param key 存储键
     * @param defaultValue 默认值
     * @returns 布尔值
     */
    static getBoolean(key: string, defaultValue: boolean = false): boolean {
        const value = this.getItem(key);
        if (value === null) {
            return defaultValue;
        }
        return value === 'true';
    }
    
    /**
     * 设置布尔值
     * @param key 存储键
     * @param value 布尔值
     */
    static setBoolean(key: string, value: boolean): void {
        this.setItem(key, value.toString());
    }
    
    /**
     * 检查键是否存在
     * @param key 存储键
     * @returns 是否存在
     */
    static hasKey(key: string): boolean {
        return this.getItem(key) !== null;
    }
    
    /**
     * 获取所有键名
     * @returns 键名数组
     */
    static getAllKeys(): string[] {
        const keys: string[] = [];
        const len = this.length();
        
        for (let i = 0; i < len; i++) {
            const key = this.key(i);
            if (key !== null) {
                keys.push(key);
            }
        }
        
        return keys;
    }
    
    /**
     * 批量获取数据
     * @param keys 键名数组
     * @returns 数据对象
     */
    static getMultiple(keys: string[]): { [key: string]: string | null } {
        const result: { [key: string]: string | null } = {};
        
        keys.forEach(key => {
            result[key] = this.getItem(key);
        });
        
        return result;
    }
    
    /**
     * 批量设置数据
     * @param data 数据对象
     */
    static setMultiple(data: { [key: string]: string }): void {
        Object.keys(data).forEach(key => {
            this.setItem(key, data[key]);
        });
    }
    
    /**
     * 批量删除数据
     * @param keys 键名数组
     */
    static removeMultiple(keys: string[]): void {
        keys.forEach(key => {
            this.removeItem(key);
        });
    }
}
