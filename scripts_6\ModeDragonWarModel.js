var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2Cfg = require("Cfg");
var $2MVC = require("MVC");
var $2Game = require("Game");
var def_ModeDragonWarModel = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t.gameMode = $2Game.Game.Mode.DRAGONWAR;
    t.roleConfig = $2Cfg.Cfg.RoleUnlock.get(30800);
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.initMonsterConfig = function (e) {
    var t = $2Cfg.Cfg.MiniGameLv.get(e);
    var o = t.lvPrefab.split("|");
    this.bgPath = o[0];
    this.battleBgPath = o[1];
    var i = t.lvid;
    this.pathList = JSON.parse(t.path);
    var n = $2Cfg.Cfg.bagMonsterLv.filter({
      lv: i
    });
    this.wallConfigs = n;
  };
  _ctor.prototype.reset = function () {};
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_ModeDragonWarModel;