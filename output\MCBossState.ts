// MCBossState.ts
// 从 MCBossState.js 转换而来

import { GameSeting } from "./GameSeting";
import { StateMachine } from "./StateMachine";

/**
 * Boss状态枚举
 */
export enum BossStateType {
    IDLE = "idle",
    PATROL = "patrol",
    CHASE = "chase",
    ATTACK = "attack",
    SKILL = "skill",
    HURT = "hurt",
    DIE = "die",
    RAGE = "rage"
}

/**
 * Boss状态机
 */
export class MCBossState extends StateMachine {
    /** Boss实例 */
    private boss: any;
    
    /** 当前状态 */
    private currentState: BossStateType = BossStateType.IDLE;
    
    /** 状态持续时间 */
    private stateDuration: number = 0;
    
    /** 愤怒模式 */
    private isRageMode: boolean = false;
    
    constructor(boss: any) {
        super();
        this.boss = boss;
        this.initStates();
    }
    
    /**
     * 初始化状态
     */
    private initStates(): void {
        // 添加各种状态
        this.addState(BossStateType.IDLE, {
            onEnter: () => this.onIdleEnter(),
            onUpdate: (dt: number) => this.onIdleUpdate(dt),
            onExit: () => this.onIdleExit()
        });
        
        this.addState(BossStateType.PATROL, {
            onEnter: () => this.onPatrolEnter(),
            onUpdate: (dt: number) => this.onPatrolUpdate(dt),
            onExit: () => this.onPatrolExit()
        });
        
        this.addState(BossStateType.CHASE, {
            onEnter: () => this.onChaseEnter(),
            onUpdate: (dt: number) => this.onChaseUpdate(dt),
            onExit: () => this.onChaseExit()
        });
        
        this.addState(BossStateType.ATTACK, {
            onEnter: () => this.onAttackEnter(),
            onUpdate: (dt: number) => this.onAttackUpdate(dt),
            onExit: () => this.onAttackExit()
        });
        
        this.addState(BossStateType.SKILL, {
            onEnter: () => this.onSkillEnter(),
            onUpdate: (dt: number) => this.onSkillUpdate(dt),
            onExit: () => this.onSkillExit()
        });
        
        this.addState(BossStateType.HURT, {
            onEnter: () => this.onHurtEnter(),
            onUpdate: (dt: number) => this.onHurtUpdate(dt),
            onExit: () => this.onHurtExit()
        });
        
        this.addState(BossStateType.DIE, {
            onEnter: () => this.onDieEnter(),
            onUpdate: (dt: number) => this.onDieUpdate(dt),
            onExit: () => this.onDieExit()
        });
        
        this.addState(BossStateType.RAGE, {
            onEnter: () => this.onRageEnter(),
            onUpdate: (dt: number) => this.onRageUpdate(dt),
            onExit: () => this.onRageExit()
        });
        
        // 设置初始状态
        this.changeState(BossStateType.IDLE);
    }
    
    /**
     * 空闲状态进入
     */
    private onIdleEnter(): void {
        this.boss.playAnimation("idle");
        this.stateDuration = 0;
    }
    
    /**
     * 空闲状态更新
     */
    private onIdleUpdate(dt: number): void {
        this.stateDuration += dt;
        
        // 检查是否需要切换到巡逻状态
        if (this.stateDuration > 2.0) {
            this.changeState(BossStateType.PATROL);
        }
        
        // 检查玩家距离
        if (this.boss.getDistanceToPlayer() < this.boss.getChaseRange()) {
            this.changeState(BossStateType.CHASE);
        }
    }
    
    /**
     * 空闲状态退出
     */
    private onIdleExit(): void {
        // 清理空闲状态
    }
    
    /**
     * 巡逻状态进入
     */
    private onPatrolEnter(): void {
        this.boss.playAnimation("walk");
        this.boss.setPatrolTarget();
        this.stateDuration = 0;
    }
    
    /**
     * 巡逻状态更新
     */
    private onPatrolUpdate(dt: number): void {
        this.stateDuration += dt;
        
        // 移动到巡逻点
        this.boss.moveToPatrolTarget(dt);
        
        // 检查是否到达巡逻点
        if (this.boss.hasReachedPatrolTarget()) {
            this.changeState(BossStateType.IDLE);
        }
        
        // 检查玩家距离
        if (this.boss.getDistanceToPlayer() < this.boss.getChaseRange()) {
            this.changeState(BossStateType.CHASE);
        }
    }
    
    /**
     * 巡逻状态退出
     */
    private onPatrolExit(): void {
        this.boss.stopMovement();
    }
    
    /**
     * 追击状态进入
     */
    private onChaseEnter(): void {
        this.boss.playAnimation("run");
        this.stateDuration = 0;
    }
    
    /**
     * 追击状态更新
     */
    private onChaseUpdate(dt: number): void {
        this.stateDuration += dt;
        
        // 追击玩家
        this.boss.chasePlayer(dt);
        
        // 检查攻击距离
        if (this.boss.getDistanceToPlayer() < this.boss.getAttackRange()) {
            this.changeState(BossStateType.ATTACK);
        }
        
        // 检查是否失去目标
        if (this.boss.getDistanceToPlayer() > this.boss.getLoseTargetRange()) {
            this.changeState(BossStateType.IDLE);
        }
    }
    
    /**
     * 追击状态退出
     */
    private onChaseExit(): void {
        this.boss.stopMovement();
    }
    
    /**
     * 攻击状态进入
     */
    private onAttackEnter(): void {
        this.boss.playAnimation("attack");
        this.boss.performAttack();
        this.stateDuration = 0;
    }
    
    /**
     * 攻击状态更新
     */
    private onAttackUpdate(dt: number): void {
        this.stateDuration += dt;
        
        // 攻击动画完成后返回追击
        if (this.stateDuration > this.boss.getAttackDuration()) {
            if (this.boss.getDistanceToPlayer() < this.boss.getChaseRange()) {
                this.changeState(BossStateType.CHASE);
            } else {
                this.changeState(BossStateType.IDLE);
            }
        }
    }
    
    /**
     * 攻击状态退出
     */
    private onAttackExit(): void {
        // 清理攻击状态
    }
    
    /**
     * 技能状态进入
     */
    private onSkillEnter(): void {
        this.boss.playAnimation("skill");
        this.boss.performSkill();
        this.stateDuration = 0;
    }
    
    /**
     * 技能状态更新
     */
    private onSkillUpdate(dt: number): void {
        this.stateDuration += dt;
        
        // 技能动画完成后返回战斗状态
        if (this.stateDuration > this.boss.getSkillDuration()) {
            this.changeState(BossStateType.CHASE);
        }
    }
    
    /**
     * 技能状态退出
     */
    private onSkillExit(): void {
        // 清理技能状态
    }
    
    /**
     * 受伤状态进入
     */
    private onHurtEnter(): void {
        this.boss.playAnimation("hurt");
        this.stateDuration = 0;
    }
    
    /**
     * 受伤状态更新
     */
    private onHurtUpdate(dt: number): void {
        this.stateDuration += dt;
        
        // 受伤动画完成后返回战斗状态
        if (this.stateDuration > this.boss.getHurtDuration()) {
            // 检查是否进入愤怒模式
            if (this.boss.getHealthPercent() < 0.3 && !this.isRageMode) {
                this.changeState(BossStateType.RAGE);
            } else {
                this.changeState(BossStateType.CHASE);
            }
        }
    }
    
    /**
     * 受伤状态退出
     */
    private onHurtExit(): void {
        // 清理受伤状态
    }
    
    /**
     * 死亡状态进入
     */
    private onDieEnter(): void {
        this.boss.playAnimation("die");
        this.boss.onDeath();
        this.stateDuration = 0;
    }
    
    /**
     * 死亡状态更新
     */
    private onDieUpdate(dt: number): void {
        this.stateDuration += dt;
        
        // 死亡动画完成后销毁
        if (this.stateDuration > this.boss.getDeathDuration()) {
            this.boss.destroy();
        }
    }
    
    /**
     * 死亡状态退出
     */
    private onDieExit(): void {
        // 清理死亡状态
    }
    
    /**
     * 愤怒状态进入
     */
    private onRageEnter(): void {
        this.boss.playAnimation("rage");
        this.boss.enterRageMode();
        this.isRageMode = true;
        this.stateDuration = 0;
    }
    
    /**
     * 愤怒状态更新
     */
    private onRageUpdate(dt: number): void {
        this.stateDuration += dt;
        
        // 愤怒动画完成后进入强化追击
        if (this.stateDuration > this.boss.getRageDuration()) {
            this.changeState(BossStateType.CHASE);
        }
    }
    
    /**
     * 愤怒状态退出
     */
    private onRageExit(): void {
        // 清理愤怒状态
    }
    
    /**
     * 受到伤害
     */
    takeDamage(damage: number): void {
        if (this.currentState !== BossStateType.DIE) {
            this.boss.takeDamage(damage);
            
            if (this.boss.isDead()) {
                this.changeState(BossStateType.DIE);
            } else {
                this.changeState(BossStateType.HURT);
            }
        }
    }
    
    /**
     * 获取当前状态
     */
    getCurrentState(): BossStateType {
        return this.currentState;
    }
    
    /**
     * 是否在愤怒模式
     */
    isInRageMode(): boolean {
        return this.isRageMode;
    }
}

export { MCBossState, BossStateType };
