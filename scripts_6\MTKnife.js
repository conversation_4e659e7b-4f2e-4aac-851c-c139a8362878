var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MTKnife = undefined;
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2NotifyID = require("NotifyID");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2AlertManager = require("AlertManager");
var $2Monster = require("Monster");
var $2Game = require("Game");
var $2BronMonsterManger = require("BronMonsterManger");
var $2CompManager = require("CompManager");
var $2NodePool = require("NodePool");
var $2ModeThrowingKnifeModel = require("ModeThrowingKnifeModel");
var $2MTKRole = require("MTKRole");
(function (e) {
  var t;
  (function (e) {
    e[e.NONE = 0] = "NONE";
    e[e.MyRound = 1] = "MyRound";
    e[e.BulletAnim = 2] = "BulletAnim";
    e[e.EnemyRound = 3] = "EnemyRound";
  })(t = e.RoundStatus || (e.RoundStatus = {}));
  var o = function (t) {
    function o(e) {
      var o = t.call(this, e) || this;
      o.cameraZoomRatio = 1;
      o.hitBackNum = 0;
      o.isHideProp = false;
      o._monster = null;
      o._mainRole = null;
      o.passParam = e;
      return o;
    }
    cc__extends(o, t);
    Object.defineProperty(o.prototype, "mode", {
      get: function () {
        return $2ModeThrowingKnifeModel.default.instance;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "monsterMap", {
      get: function () {
        return this._monsterMap;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.loadMap = function (t, o) {
      var n = this;
      this.gameNode = t;
      this.miniGameCfg = $2Cfg.Cfg.MiniGameLv.get(this.passParam.id);
      this._entityNode = t.getChildByName("entityNode");
      this._bulletNode = t.getChildByName("bulletNode");
      this._mapNode = t.getChildByName("mapNode");
      this._botEffectNode = t.getChildByName("botEffectNode");
      this._topEffectNode = t.getChildByName("topEffectNode");
      this.topUINode = t.getORaddChildByName("topUINode");
      this.behitUI = t.getORaddChildByName("behitUI");
      this._finishCall = o;
      $2Manager.Manager.loader.loadPrefab(this.miniGameCfg.lvPrefab).then(function (t) {
        t.parent = n._mapNode;
        t.zIndex = -1;
        n.myTiledMap = n._mapNode.getComByChild(cc.TiledMap);
        var o = t.width / 2 - $2GameUtil.GameUtil.getDesignSize.width / 2;
        var i = t.height / 2 - $2GameUtil.GameUtil.getDesignSize.height / 2;
        n.gameCamera.setMoveLimt([-o, o], [-i, i]);
        var a = n.getObjPos(n.myTiledMap.getObjectGroup("born").getObject("player"));
        n.gameCamera.setPosition(a);
        n.gameCamera.lookPos.set(a);
        n.createRole(a).then(function () {
          var t = n.getObjPos(n.myTiledMap.getObjectGroup("born").getObject("monster"));
          n.createMonster($2Cfg.Cfg.bagMonsterLv.filter({
            lv: n.miniGameCfg.lvid
          })[0], t).then(function (t) {
            $2Game.Game.tween(n.gameCamera).set({
              lookPos: t.position
            }).delay(2).set({
              lookPos: n.mainRole.position
            }).call(function () {
              n.bronMonsterMgr.changeGameStatus(e.RoundStatus.MyRound);
            }).start();
          });
        });
      });
      this.gameCamera.setZoomRatio(this.cameraZoomRatio);
      this.bronMonsterMgr = this.gameNode.getORaddComponent(i);
      this.bronMonsterMgr.init();
      this._finishCall && this._finishCall();
    };
    o.prototype.gameEnd = function () {
      var e = this;
      $2Game.Game.timerOnce(function () {
        e.gameState = $2Game.Game.State.NONE;
      }, .5);
    };
    o.prototype.createRole = function (e) {
      var t = this;
      return new Promise(function (o) {
        $2NodePool.NodePool.spawn("entity/fight/ModeTKnife/role").setNodeAssetFinishCall(function (i) {
          var n = i.getComponent($2MTKRole.default);
          i.parent = t._entityNode;
          n.setPosition(e);
          n.init();
          n.setRole();
          $2CompManager.default.Instance.registerComp(n);
          t.mainRole = n;
          o(n);
        });
      });
    };
    o.prototype.createMonster = function (e, t) {
      var o = this;
      return new Promise(function (i) {
        var n = $2Cfg.Cfg.Monster.get(e.monId[0]);
        $2NodePool.NodePool.spawn("entity/fight/ModeTKnife/monster").setNodeAssetFinishCall(function (r) {
          var s;
          var c;
          if (!r) {
            return console.error("怪物生成错误", null == n ? undefined : n.name);
          }
          var u = r.getComponent($2Monster.Monster);
          o._monster = u;
          r.parent = o._entityNode;
          u.setPosition(t);
          u.monsterId = n.id;
          u.lvCfg = e;
          u.init();
          null === (s = u.steering) || undefined === s || s.seekOn();
          null === (c = u.steering) || undefined === c || c.setTargetAgent1(o.mainRole);
          o._monsterMap.set(u.ID, u);
          o.elementMap.set(u.ID, u);
          i(u);
          $2Notifier.Notifier.send($2ListenID.ListenID.Game_LoadFinish);
        });
      });
    };
    o.prototype.getObjPos = function (e) {
      return cc.v2(e.x - this.myTiledMap.node.width / 2 + e.width / 2, e.y - this.myTiledMap.node.height / 2 - e.height);
    };
    Object.defineProperty(o.prototype, "monster", {
      get: function () {
        return this._monster;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "mainRole", {
      get: function () {
        return this._mainRole;
      },
      set: function (e) {
        this._mainRole = e;
      },
      enumerable: false,
      configurable: true
    });
    return o;
  }($2Game.Game.Mgr);
  e.Mgr = o;
  var i = function (e) {
    function o() {
      var o = null !== e && e.apply(this, arguments) || this;
      o._batchNum = 0;
      o.cutStaus = t.NONE;
      o.countDown = 0;
      return o;
    }
    cc__extends(o, e);
    Object.defineProperty(o.prototype, "batchNum", {
      get: function () {
        return this._batchNum;
      },
      set: function (e) {
        this._batchNum = e;
        0 != e && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameRound, e);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "game", {
      get: function () {
        return $2Game.Game.mgr;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.init = function () {};
    Object.defineProperty(o.prototype, "curBullet", {
      get: function () {
        return this.game.curBullet;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.onUpdate = function () {
      var e;
      if (this.game.gameState == $2Game.Game.State.START && this.cutStaus == t.BulletAnim) {
        if (null === (e = this.curBullet) || undefined === e ? undefined : e.isActive) {
          (this.curBullet.position.x < this.game.gameCamera.moveLimtX[0] || this.curBullet.position.x > this.game.gameCamera.moveLimtX[1]) && this.curBullet.vo.lifeTime;
        } else {
          this.changeGameStatus(this.cutRoundType == t.MyRound ? t.EnemyRound : t.MyRound);
        }
      }
    };
    o.prototype.changeListener = function (t) {
      e.prototype.changeListener.call(this, t);
    };
    o.prototype.changeGameStatus = function (e) {
      var o = this;
      switch (e) {
        case t.MyRound:
          this.game.gameCamera.setTargetNode(null);
          $2Game.Game.tween(this.game.gameCamera).set({
            lookPos: this.mainRole.position
          }).start();
          this.cutRoundType = e;
          $2AlertManager.AlertManager.showNormalTips("我方回合");
          break;
        case t.EnemyRound:
          this.game.gameCamera.setTargetNode(null);
          this.game.gameCamera.lookPos.set(this.game.monsterMap.arr[0].position);
          this.cutRoundType = e;
          $2Game.Game.tween(this.game.gameCamera).set({
            lookPos: this.game.monsterMap.arr[0].position
          }).delay(1).call(function () {
            o.game.monsterMap.arr[0].aiFire();
          }).start();
          $2AlertManager.AlertManager.showNormalTips("敌方回合");
          break;
        case t.BulletAnim:
      }
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameRoundType, e);
      this.cutStaus = e;
    };
    return o;
  }($2BronMonsterManger.BronMonsterManger);
  e.SpawningMgr = i;
  var M = cc.v2();
  e.maxPower = 200;
  var b = function (t) {
    function o() {
      var e = null !== t && t.apply(this, arguments) || this;
      e.fireNum = 0;
      return e;
    }
    cc__extends(o, t);
    Object.defineProperty(o.prototype, "game", {
      get: function () {
        return $2Game.Game.mgr;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "aimData", {
      get: function () {
        return this._aimData;
      },
      set: function (t) {
        this._aimData = t;
        t.power = cc.misc.clampf(t.power, 10, e.maxPower);
        M.set($2GameUtil.GameUtil.AngleAndLenToPos(t.angle, t.power));
        this.line.node.active || this.ower.setAnimation("aim", true);
        this.line.node.setAttribute({
          active: true,
          position: this.ower._bodyPosition,
          angle: (t.angle + 180) % 360
        });
        this.line.fillRange = t.power / e.maxPower;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.fire = function (t, o) {
      var i = this;
      undefined === o && (o = .5);
      this.line.node.setActive(false);
      var n = this.ower.firstSkill.getBulletVo({
        startPos: this.ower.bodyPosition
      });
      n.lifeTime = 5;
      n.speed = 1200;
      n.hurt.baseVal = 50;
      n.crossNum = 1;
      t && t(n);
      $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick, true);
      this.ower.delayByGame(function () {
        $2Game.Game.mgr.spawnBullet(n.bulletPath, n, {
          parent: i.game._bulletNode,
          active: false
        }).then(function (t) {
          t.collider.setActive(false);
          t.node.setActive(true);
          console.log("fire angle", i.aimData.angle, " power: ", i.aimData.power);
          t.set({
            angle: (i.aimData.angle + 180) % 360,
            power: i.aimData.power
          });
          i.game.gameCamera.setTargetNode(t);
          i.game.curBullet = t;
          i.game.bronMonsterMgr.changeGameStatus(e.RoundStatus.BulletAnim);
          i.ower.delayByGame(function () {
            t.collider.setActive(true);
          }, .3);
          $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick, false);
        });
      }, o);
      this.fireNum++;
      this.ower.mySkeleton.playQueue(["attack", "idle"], true);
    };
    return o;
  }($2GameSeting.GameSeting.CompBase);
  e.KnifeController = b;
})(exports.MTKnife || (exports.MTKnife = {}));