import { Cfg } from "./Cfg";
import { GameatrCfg } from "./GameatrCfg";
import { SoundCfg } from "./SoundCfg";
import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { GameUtil } from "./GameUtil";
import { BaseEntity } from "./BaseEntity";
import { Dragon } from "./Dragon";
import { Game } from "./Game";
import { PropertyVo } from "./PropertyVo";
import { MChains } from "./MChains";
import { TideDefendModel } from "./TideDefendModel";

const { ccclass } = cc._decorator;

@ccclass
class MonstarTideDragon extends Dragon {
    public baglvCfg: any = null;

    get mode(): TideDefendModel {
        return TideDefendModel.default.instance;
    }

    get game(): any {
        return Game.mgr;
    }

    public setInfo(index: number, paths: any[]): void {
        super.setInfo(index, paths);
        
        const dragonId = this.baglvCfg.monId ? this.baglvCfg.monId[0] : 999999991;
        this.roundMonster = Object.values(this.mode.dragonList[dragonId]);
        
        const difficultyConfigs = Manager.vo.switchVo.dragonDiff.filter((config: any) => 
            config[0] === this.game.miniGameCfg.lvid
        );
        
        this.roundMonster.forEach((monster: any, index: number) => {
            const diffConfig = difficultyConfigs.find((config: any) => 
                index >= config[1] && index < config[2]
            );
            if (diffConfig) {
                monster.hp *= 1 + diffConfig[3];
            }
        });
        
        const firstMonster = this.roundMonster[0];
        this.property = new PropertyVo.Property.Vo(this, {
            speed: firstMonster.speed,
            atk: firstMonster.atk,
            hp: firstMonster.hp
        });
        
        this.rageSpeed = 0.5;
        this.maxSpeed = firstMonster.speed;
        this.monCfg = Cfg.Monster.get(GameUtil.randomArr(firstMonster.monId));
        this.setAmClip();
        this.isAngleHade = this.monCfg.spine.includes("_f");
        
        this.monCfg.buff?.forEach((buffId: number) => this.addBuff(buffId));
        this.monCfg.skill?.forEach((skillId: number) => this.addSkill(skillId));
    }

    public setAmClip(): void {
        if (this.monCfg && this.monCfg.spine) {
            Manager.loader.loadSpineNode(this.monCfg.spine, {
                nodeAttr: {
                    parent: this.node,
                    position: cc.Vec2.ZERO
                },
                spAttr: {
                    loop: true,
                    defaultAnim: "idle",
                    isPlayerOnLoad: true
                }
            }).then((skeleton: any) => {
                this.mySkeleton = skeleton;
                this.roleNode = skeleton.node;
                
                const moveSpeedBuff = this.buffMgr?.attrMapAll.getor(GameatrCfg.GameatrDefine.movespeed, 0) || 0;
                if (moveSpeedBuff > 0) {
                    this.playAction("anger", true);
                }
            });
        } else {
            console.log("没有配置龙骨");
        }
    }

    public createBody(monsterData: any, bodyIndex: number): Promise<any> {
        return new Promise((resolve) => {
            if (monsterData.hp > 1) {
                monsterData.hp = Math.max(1, Math.round(monsterData.hp * (1 + this.mode.gmDiff)));
            }
            
            const dragonHeadCfg = this.getDragonHeadCfg();
            if (dragonHeadCfg) {
                monsterData.hp = monsterData.hp + dragonHeadCfg.hp;
            }
            
            super.createBody(monsterData, bodyIndex).then((body: any) => {
                resolve(body);
            });
        });
    }

    public toDead(): void {
        this.bodyList.forReverse((body: any) => {
            body.isActive = false;
            body.removeEntityToUpdate();
        });
        super.toDead();
    }

    public onUpdate(dt: number): void {
        super.onUpdate(dt);
        this.disMonster();
    }

    public disMonster(): void {
        if (this.loadIndex > 2 && this.bodyList.length === 0) {
            this.toDead();
        } else if (this.mode.bDragonBoss(this.baglvCfg) && this.position.y < this.mode.role.haedPosition.y) {
            Notifier.send(ListenID.Fight_End, false);
            this.scheduleOnce(() => {
                this.toDead();
            }, 0.5);
        } else if (this.lastPosY < -100) {
            this.toDead();
        }
    }

    public getDragonHeadCfg(): any {
        return this.baglvCfg;
    }

    public onKillMonster(monster: any): void {
        this.killLen++;
        this.game.killMonsterNum++;
        
        const section = monster.section;
        const crazyConfig = Manager.vo.switchVo.dragonCrzay.find((config: any) => 
            section >= config[0] && section < config[1]
        );
        
        if (crazyConfig && Game.weightFloat(crazyConfig[2])) {
            const rageBuff = {
                id: 999991,
                name: "狂暴",
                type: 1,
                time: 4,
                attr: [GameatrCfg.GameatrDefine.movespeed],
                value: [[this.rageSpeed]]
            };
            this.addBuffByData(rageBuff);
        }
        
        this.addBuffByData({
            id: 999992,
            name: "弱化",
            type: 1,
            time: 1,
            attr: [GameatrCfg.GameatrDefine.movespeed],
            value: [[-0.5]]
        });
        
        this.isRetreat = true;
        Game.tween(this).stopLast().delay(0.6).set({
            isRetreat: false
        }).start();
        
        if (this.game.passType === MChains.PassType.Move) {
            this.curIndex -= Math.max(Math.round(monster.size / 2), 0);
        } else {
            this.curIndex -= Math.max(Math.round(monster.size), 0);
        }
        
        this.bodyList.delete(monster);
        if (this.bodyList.length === 0 && this.loadIndex > 5) {
            this.toDead();
        }
        this._dt = 0;
    }

    public onCollisionEnter(collision: any): void {
        if (collision.comp?.entityType === BaseEntity.EntityType.Bullet) {
            const effectPos = cc.v2(collision.comp.position.x + 5, collision.comp.position.y + 5);
            Manager.audio.playAudio(SoundCfg.SoundDefine.skill_zj);
            Game.mgr.showEffectByType("entity/fight/effect/Effect_Bomb", effectPos, true, 0.3, {
                parent: this.game.botEffectNode
            });
        }
    }
}

export default MonstarTideDragon;
