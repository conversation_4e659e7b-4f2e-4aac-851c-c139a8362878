import { Cfg } from "./Cfg";
import { Notifier } from "./Notifier";
import { NotifyID } from "./NotifyID";
import { GameSeting } from "./GameSeting";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { GameUtil } from "./GameUtil";
import { AlertManager } from "./AlertManager";
import { Monster } from "./Monster";
import { Game } from "./Game";
import { BronMonsterManger } from "./BronMonsterManger";
import { CompManager } from "./CompManager";
import { NodePool } from "./NodePool";
import { ModeThrowingKnifeModel } from "./ModeThrowingKnifeModel";
import { MTKRole } from "./MTKRole";

export namespace MTKnife {
    export enum RoundStatus {
        NONE = 0,
        MyRound = 1,
        BulletAnim = 2,
        EnemyRound = 3
    }

    export class Mgr extends Game.Mgr {
        public cameraZoomRatio: number = 1;
        public hitBackNum: number = 0;
        public isHideProp: boolean = false;
        private _monster: Monster = null;
        private _mainRole: MTKRole = null;
        public passParam: any;

        constructor(passParam: any) {
            super(passParam);
            this.passParam = passParam;
        }

        get mode(): ModeThrowingKnifeModel {
            return ModeThrowingKnifeModel.default.instance;
        }

        get monsterMap(): any {
            return this._monsterMap;
        }

        public loadMap(gameNode: cc.Node, finishCall?: Function): void {
            this.gameNode = gameNode;
            this.miniGameCfg = Cfg.MiniGameLv.get(this.passParam.id);
            this._entityNode = gameNode.getChildByName("entityNode");
            this._bulletNode = gameNode.getChildByName("bulletNode");
            this._mapNode = gameNode.getChildByName("mapNode");
            this._botEffectNode = gameNode.getChildByName("botEffectNode");
            this._topEffectNode = gameNode.getChildByName("topEffectNode");
            this.topUINode = gameNode.getORaddChildByName("topUINode");
            this.behitUI = gameNode.getORaddChildByName("behitUI");
            this._finishCall = finishCall;

            Manager.loader.loadPrefab(this.miniGameCfg.lvPrefab).then((mapNode: cc.Node) => {
                mapNode.parent = this._mapNode;
                mapNode.zIndex = -1;
                this.myTiledMap = this._mapNode.getComByChild(cc.TiledMap);
                
                const offsetX = mapNode.width / 2 - GameUtil.getDesignSize.width / 2;
                const offsetY = mapNode.height / 2 - GameUtil.getDesignSize.height / 2;
                this.gameCamera.setMoveLimt([-offsetX, offsetX], [-offsetY, offsetY]);
                
                const playerPos = this.getObjPos(this.myTiledMap.getObjectGroup("born").getObject("player"));
                this.gameCamera.setPosition(playerPos);
                this.gameCamera.lookPos.set(playerPos);
                
                this.createRole(playerPos).then(() => {
                    const monsterPos = this.getObjPos(this.myTiledMap.getObjectGroup("born").getObject("monster"));
                    this.createMonster(Cfg.bagMonsterLv.filter({
                        lv: this.miniGameCfg.lvid
                    })[0], monsterPos).then((monster: Monster) => {
                        Game.tween(this.gameCamera).set({
                            lookPos: monster.position
                        }).delay(2).set({
                            lookPos: this.mainRole.position
                        }).call(() => {
                            this.bronMonsterMgr.changeGameStatus(RoundStatus.MyRound);
                        }).start();
                    });
                });
            });

            this.gameCamera.setZoomRatio(this.cameraZoomRatio);
            this.bronMonsterMgr = this.gameNode.getORaddComponent(SpawningMgr);
            this.bronMonsterMgr.init();
            this._finishCall && this._finishCall();
        }

        public gameEnd(): void {
            Game.timerOnce(() => {
                this.gameState = Game.State.NONE;
            }, 0.5);
        }

        public createRole(position: cc.Vec2): Promise<MTKRole> {
            return new Promise((resolve) => {
                NodePool.spawn("entity/fight/ModeTKnife/role").setNodeAssetFinishCall((roleNode: cc.Node) => {
                    const role = roleNode.getComponent(MTKRole.default);
                    roleNode.parent = this._entityNode;
                    role.setPosition(position);
                    role.init();
                    role.setRole();
                    CompManager.default.Instance.registerComp(role);
                    this.mainRole = role;
                    resolve(role);
                });
            });
        }

        public createMonster(lvCfg: any, position: cc.Vec2): Promise<Monster> {
            return new Promise((resolve) => {
                const monsterCfg = Cfg.Monster.get(lvCfg.monId[0]);
                NodePool.spawn("entity/fight/ModeTKnife/monster").setNodeAssetFinishCall((monsterNode: cc.Node) => {
                    if (!monsterNode) {
                        return console.error("怪物生成错误", monsterCfg?.name);
                    }
                    
                    const monster = monsterNode.getComponent(Monster);
                    this._monster = monster;
                    monsterNode.parent = this._entityNode;
                    monster.setPosition(position);
                    monster.monsterId = monsterCfg.id;
                    monster.lvCfg = lvCfg;
                    monster.init();
                    monster.steering?.seekOn();
                    monster.steering?.setTargetAgent1(this.mainRole);
                    this._monsterMap.set(monster.ID, monster);
                    this.elementMap.set(monster.ID, monster);
                    resolve(monster);
                    Notifier.send(ListenID.Game_LoadFinish);
                });
            });
        }

        public getObjPos(obj: any): cc.Vec2 {
            return cc.v2(
                obj.x - this.myTiledMap.node.width / 2 + obj.width / 2,
                obj.y - this.myTiledMap.node.height / 2 - obj.height
            );
        }

        get monster(): Monster {
            return this._monster;
        }

        get mainRole(): MTKRole {
            return this._mainRole;
        }

        set mainRole(role: MTKRole) {
            this._mainRole = role;
        }
    }

    export class SpawningMgr extends BronMonsterManger {
        private _batchNum: number = 0;
        public cutStaus: RoundStatus = RoundStatus.NONE;
        public countDown: number = 0;

        get batchNum(): number {
            return this._batchNum;
        }

        set batchNum(value: number) {
            this._batchNum = value;
            if (value !== 0) {
                Notifier.send(ListenID.Fight_GameRound, value);
            }
        }

        get game(): Mgr {
            return Game.mgr as Mgr;
        }

        public init(): void {}

        get curBullet(): any {
            return this.game.curBullet;
        }

        public onUpdate(): void {
            if (this.game.gameState === Game.State.START && this.cutStaus === RoundStatus.BulletAnim) {
                if (this.curBullet?.isActive) {
                    if ((this.curBullet.position.x < this.game.gameCamera.moveLimtX[0] || 
                         this.curBullet.position.x > this.game.gameCamera.moveLimtX[1]) && 
                         this.curBullet.vo.lifeTime) {
                        // Handle bullet out of bounds
                    }
                } else {
                    this.changeGameStatus(this.cutRoundType === RoundStatus.MyRound ? RoundStatus.EnemyRound : RoundStatus.MyRound);
                }
            }
        }

        public changeListener(listener: boolean): void {
            super.changeListener(listener);
        }

        public changeGameStatus(status: RoundStatus): void {
            switch (status) {
                case RoundStatus.MyRound:
                    this.game.gameCamera.setTargetNode(null);
                    Game.tween(this.game.gameCamera).set({
                        lookPos: this.mainRole.position
                    }).start();
                    this.cutRoundType = status;
                    AlertManager.showNormalTips("我方回合");
                    break;
                case RoundStatus.EnemyRound:
                    this.game.gameCamera.setTargetNode(null);
                    this.game.gameCamera.lookPos.set(this.game.monsterMap.arr[0].position);
                    this.cutRoundType = status;
                    Game.tween(this.game.gameCamera).set({
                        lookPos: this.game.monsterMap.arr[0].position
                    }).delay(1).call(() => {
                        this.game.monsterMap.arr[0].aiFire();
                    }).start();
                    AlertManager.showNormalTips("敌方回合");
                    break;
                case RoundStatus.BulletAnim:
                    break;
            }
            Notifier.send(ListenID.Fight_GameRoundType, status);
            this.cutStaus = status;
        }
    }

    const aimVector = cc.v2();
    export const maxPower = 200;

    export class KnifeController extends GameSeting.CompBase {
        public fireNum: number = 0;
        private _aimData: any;

        get game(): Mgr {
            return Game.mgr as Mgr;
        }

        get aimData(): any {
            return this._aimData;
        }

        set aimData(data: any) {
            this._aimData = data;
            data.power = cc.misc.clampf(data.power, 10, maxPower);
            aimVector.set(GameUtil.AngleAndLenToPos(data.angle, data.power));
            
            if (!this.line.node.active) {
                this.ower.setAnimation("aim", true);
            }
            
            this.line.node.setAttribute({
                active: true,
                position: this.ower._bodyPosition,
                angle: (data.angle + 180) % 360
            });
            this.line.fillRange = data.power / maxPower;
        }

        public fire(callback?: Function, delay: number = 0.5): void {
            this.line.node.setActive(false);
            
            const bulletVo = this.ower.firstSkill.getBulletVo({
                startPos: this.ower.bodyPosition
            });
            bulletVo.lifeTime = 5;
            bulletVo.speed = 1200;
            bulletVo.hurt.baseVal = 50;
            bulletVo.crossNum = 1;
            
            callback && callback(bulletVo);
            Notifier.send(NotifyID.Game_BanClick, true);
            
            this.ower.delayByGame(() => {
                Game.mgr.spawnBullet(bulletVo.bulletPath, bulletVo, {
                    parent: this.game._bulletNode,
                    active: false
                }).then((bullet: any) => {
                    bullet.collider.setActive(false);
                    bullet.node.setActive(true);
                    console.log("fire angle", this.aimData.angle, " power: ", this.aimData.power);
                    
                    bullet.set({
                        angle: (this.aimData.angle + 180) % 360,
                        power: this.aimData.power
                    });
                    
                    this.game.gameCamera.setTargetNode(bullet);
                    this.game.curBullet = bullet;
                    this.game.bronMonsterMgr.changeGameStatus(RoundStatus.BulletAnim);
                    
                    this.ower.delayByGame(() => {
                        bullet.collider.setActive(true);
                    }, 0.3);
                    
                    Notifier.send(NotifyID.Game_BanClick, false);
                });
            }, delay);
            
            this.fireNum++;
            this.ower.mySkeleton.playQueue(["attack", "idle"], true);
        }
    }
}
