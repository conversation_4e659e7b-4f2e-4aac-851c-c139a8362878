// MBRebound.ts
// 从 MBRebound.js 转换而来

import { Cfg } from "./Cfg";
import { Notifier } from "./Notifier";
import { NotifyID } from "./NotifyID";
import { GameSeting } from "./GameSeting";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { FPolygonCollider } from "./FPolygonCollider";
import { Intersection } from "./Intersection";
import { GameUtil } from "./GameUtil";
import { RecordVo } from "./RecordVo";
import { AlertManager } from "./AlertManager";
import { Game } from "./Game";
import { BronMonsterManger } from "./BronMonsterManger";
import { CompManager } from "./CompManager";
import { MTKnife } from "./MTKnife";
import { NodePool } from "./NodePool";
import { ModeBulletsReboundModel } from "./ModeBulletsReboundModel";
import { MBRMonster } from "./MBRMonster";
import { MBRRole } from "./MBRRole";

/**
 * 回合状态枚举
 */
export enum RoundStatus {
    NONE = 0,
    MyRound = 1,
    BulletAnim = 2,
    EnemyRound = 3,
    Determine = 4
}

/**
 * 广告池类型枚举
 */
enum AdPoolType {
    NormalBuff = "NormalBuff",
    HighBuff = "HighBuff"
}

/**
 * 广告池配置接口
 */
interface AdPoolConfig {
    resetNum: number;
    getAll: number;
}

/**
 * 子弹反弹模式管理器
 */
export class MBRebound extends GameSeting.CompBase {
    /** 广告池映射 */
    private poolADMap: { [key in AdPoolType]: AdPoolConfig } = {
        [AdPoolType.NormalBuff]: {
            resetNum: 3,
            getAll: 0
        },
        [AdPoolType.HighBuff]: {
            resetNum: 1,
            getAll: 0
        }
    };
    
    /** 免费次数 */
    public freeTime: number = 1;
    
    /** 击杀数量 */
    public killNum: number = 0;
    
    /** 倒计时时间 */
    public countdownTime: number = 0;
    
    /** 广告刷新装备次数 */
    public adRefreshEquip: number = 9999;
    
    /** 当前回合状态 */
    private currentRoundStatus: RoundStatus = RoundStatus.NONE;
    
    /** 游戏是否暂停 */
    private isPaused: boolean = false;
    
    /** 怪物管理器 */
    private monsterManager: BronMonsterManger;
    
    /** 角色实例 */
    private roleInstance: MBRRole;
    
    /** 怪物列表 */
    private monsters: MBRMonster[] = [];
    
    /** 子弹列表 */
    private bullets: any[] = [];
    
    /** 游戏数据模型 */
    private gameModel: ModeBulletsReboundModel;
    
    constructor() {
        super();
        this.initializeGame();
    }
    
    /**
     * 初始化游戏
     */
    private initializeGame(): void {
        this.gameModel = Manager.getModel(ModeBulletsReboundModel);
        this.monsterManager = new BronMonsterManger();
        this.setupEventListeners();
        this.resetGameState();
    }
    
    /**
     * 设置事件监听
     */
    private setupEventListeners(): void {
        Notifier.on(ListenID.BULLET_HIT_MONSTER, this.onBulletHitMonster, this);
        Notifier.on(ListenID.MONSTER_DIED, this.onMonsterDied, this);
        Notifier.on(ListenID.ROUND_END, this.onRoundEnd, this);
        Notifier.on(ListenID.GAME_PAUSE, this.onGamePause, this);
        Notifier.on(ListenID.GAME_RESUME, this.onGameResume, this);
    }
    
    /**
     * 重置游戏状态
     */
    private resetGameState(): void {
        this.currentRoundStatus = RoundStatus.NONE;
        this.killNum = 0;
        this.countdownTime = 0;
        this.isPaused = false;
        this.monsters = [];
        this.bullets = [];
    }
    
    /**
     * 开始游戏
     */
    startGame(): void {
        this.resetGameState();
        this.currentRoundStatus = RoundStatus.MyRound;
        this.spawnInitialMonsters();
        this.startRound();
    }
    
    /**
     * 生成初始怪物
     */
    private spawnInitialMonsters(): void {
        const monsterConfigs = this.gameModel.getCurrentLevelMonsters();
        
        monsterConfigs.forEach((config: any) => {
            const monster = this.createMonster(config);
            if (monster) {
                this.monsters.push(monster);
            }
        });
    }
    
    /**
     * 创建怪物
     * @param config 怪物配置
     * @returns 怪物实例
     */
    private createMonster(config: any): MBRMonster | null {
        try {
            const monster = NodePool.get("MBRMonster") as MBRMonster;
            if (monster) {
                monster.initWithConfig(config);
                return monster;
            }
        } catch (error) {
            console.error("创建怪物失败:", error);
        }
        return null;
    }
    
    /**
     * 开始回合
     */
    private startRound(): void {
        if (this.currentRoundStatus !== RoundStatus.MyRound) {
            return;
        }
        
        this.updateUI();
        this.enablePlayerInput();
        
        // 发送回合开始通知
        Notifier.send(NotifyID.ROUND_STARTED, {
            roundStatus: this.currentRoundStatus,
            killNum: this.killNum
        });
    }
    
    /**
     * 更新UI
     */
    private updateUI(): void {
        Notifier.send(NotifyID.UPDATE_GAME_UI, {
            killNum: this.killNum,
            roundStatus: this.currentRoundStatus,
            countdownTime: this.countdownTime
        });
    }
    
    /**
     * 启用玩家输入
     */
    private enablePlayerInput(): void {
        // 启用玩家操作
        if (this.roleInstance) {
            this.roleInstance.enableInput();
        }
    }
    
    /**
     * 禁用玩家输入
     */
    private disablePlayerInput(): void {
        // 禁用玩家操作
        if (this.roleInstance) {
            this.roleInstance.disableInput();
        }
    }
    
    /**
     * 子弹击中怪物事件
     * @param data 事件数据
     */
    private onBulletHitMonster(data: any): void {
        const { bullet, monster, damage } = data;
        
        if (monster && monster.takeDamage) {
            monster.takeDamage(damage);
            
            // 播放击中特效
            this.playHitEffect(monster.getPosition());
            
            // 记录击中
            RecordVo.record("bullet_hit", {
                monsterId: monster.getId(),
                damage: damage,
                timestamp: Date.now()
            });
        }
    }
    
    /**
     * 怪物死亡事件
     * @param data 事件数据
     */
    private onMonsterDied(data: any): void {
        const { monster } = data;
        
        this.killNum++;
        this.removeMonster(monster);
        
        // 播放死亡特效
        this.playDeathEffect(monster.getPosition());
        
        // 检查是否所有怪物都被击杀
        if (this.monsters.length === 0) {
            this.onAllMonstersKilled();
        }
        
        // 更新UI
        this.updateUI();
        
        // 记录击杀
        RecordVo.record("monster_killed", {
            monsterId: monster.getId(),
            killNum: this.killNum,
            timestamp: Date.now()
        });
    }
    
    /**
     * 移除怪物
     * @param monster 怪物实例
     */
    private removeMonster(monster: MBRMonster): void {
        const index = this.monsters.indexOf(monster);
        if (index !== -1) {
            this.monsters.splice(index, 1);
            NodePool.put("MBRMonster", monster);
        }
    }
    
    /**
     * 所有怪物被击杀
     */
    private onAllMonstersKilled(): void {
        this.currentRoundStatus = RoundStatus.Determine;
        this.disablePlayerInput();
        
        // 延迟显示胜利界面
        setTimeout(() => {
            this.showVictoryUI();
        }, 1000);
    }
    
    /**
     * 显示胜利界面
     */
    private showVictoryUI(): void {
        const score = this.calculateScore();
        const stars = this.calculateStars(score);
        
        Notifier.send(NotifyID.LEVEL_COMPLETED, {
            score: score,
            stars: stars,
            killNum: this.killNum
        });
    }
    
    /**
     * 计算分数
     * @returns 分数
     */
    private calculateScore(): number {
        // 基础分数 + 击杀奖励 + 时间奖励
        const baseScore = 1000;
        const killBonus = this.killNum * 100;
        const timeBonus = Math.max(0, this.countdownTime * 10);
        
        return baseScore + killBonus + timeBonus;
    }
    
    /**
     * 计算星级
     * @param score 分数
     * @returns 星级
     */
    private calculateStars(score: number): number {
        if (score >= 3000) return 3;
        if (score >= 2000) return 2;
        return 1;
    }
    
    /**
     * 播放击中特效
     * @param position 位置
     */
    private playHitEffect(position: cc.Vec2): void {
        // 播放击中特效
        CompManager.playEffect("hit_effect", position);
    }
    
    /**
     * 播放死亡特效
     * @param position 位置
     */
    private playDeathEffect(position: cc.Vec2): void {
        // 播放死亡特效
        CompManager.playEffect("death_effect", position);
    }
    
    /**
     * 回合结束事件
     */
    private onRoundEnd(): void {
        this.currentRoundStatus = RoundStatus.EnemyRound;
        this.disablePlayerInput();
        
        // 处理敌人回合逻辑
        this.processEnemyTurn();
    }
    
    /**
     * 处理敌人回合
     */
    private processEnemyTurn(): void {
        // 敌人行动逻辑
        this.monsters.forEach(monster => {
            if (monster.isAlive()) {
                monster.performAction();
            }
        });
        
        // 回合结束后切换到玩家回合
        setTimeout(() => {
            this.currentRoundStatus = RoundStatus.MyRound;
            this.startRound();
        }, 2000);
    }
    
    /**
     * 游戏暂停事件
     */
    private onGamePause(): void {
        this.isPaused = true;
        this.disablePlayerInput();
    }
    
    /**
     * 游戏恢复事件
     */
    private onGameResume(): void {
        this.isPaused = false;
        if (this.currentRoundStatus === RoundStatus.MyRound) {
            this.enablePlayerInput();
        }
    }
    
    /**
     * 获取当前回合状态
     */
    getCurrentRoundStatus(): RoundStatus {
        return this.currentRoundStatus;
    }
    
    /**
     * 获取击杀数量
     */
    getKillNum(): number {
        return this.killNum;
    }
    
    /**
     * 获取剩余怪物数量
     */
    getRemainingMonsterCount(): number {
        return this.monsters.length;
    }
    
    /**
     * 是否游戏结束
     */
    isGameOver(): boolean {
        return this.currentRoundStatus === RoundStatus.Determine;
    }
    
    /**
     * 销毁
     */
    destroy(): void {
        // 移除事件监听
        Notifier.off(ListenID.BULLET_HIT_MONSTER, this.onBulletHitMonster, this);
        Notifier.off(ListenID.MONSTER_DIED, this.onMonsterDied, this);
        Notifier.off(ListenID.ROUND_END, this.onRoundEnd, this);
        Notifier.off(ListenID.GAME_PAUSE, this.onGamePause, this);
        Notifier.off(ListenID.GAME_RESUME, this.onGameResume, this);
        
        // 清理资源
        this.monsters.forEach(monster => {
            NodePool.put("MBRMonster", monster);
        });
        
        this.monsters = [];
        this.bullets = [];
        
        super.destroy();
    }
}

export { MBRebound, RoundStatus };
