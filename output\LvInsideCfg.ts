// LvInsideCfg.ts
// 从 LvInsideCfg.js 转换而来

import { TConfig } from "./TConfig";

/**
 * 关卡内部配置读取器
 */
export class LvInsideCfgReader extends TConfig {
    protected _name: string = "LvInside";
    
    constructor() {
        super();
    }
    
    /**
     * 获取关卡内部配置
     * @param levelId 关卡ID
     * @returns 关卡内部配置数据
     */
    getLevelInsideConfig(levelId: number): any {
        return this.getConfig(levelId);
    }
    
    /**
     * 获取关卡内敌人配置
     * @param levelId 关卡ID
     * @returns 敌人配置列表
     */
    getEnemyConfigs(levelId: number): any[] {
        const config = this.getLevelInsideConfig(levelId);
        return config ? config.enemies || [] : [];
    }
    
    /**
     * 获取关卡内道具配置
     * @param levelId 关卡ID
     * @returns 道具配置列表
     */
    getItemConfigs(levelId: number): any[] {
        const config = this.getLevelInsideConfig(levelId);
        return config ? config.items || [] : [];
    }
    
    /**
     * 获取关卡内障碍物配置
     * @param levelId 关卡ID
     * @returns 障碍物配置列表
     */
    getObstacleConfigs(levelId: number): any[] {
        const config = this.getLevelInsideConfig(levelId);
        return config ? config.obstacles || [] : [];
    }
    
    /**
     * 获取关卡内触发器配置
     * @param levelId 关卡ID
     * @returns 触发器配置列表
     */
    getTriggerConfigs(levelId: number): any[] {
        const config = this.getLevelInsideConfig(levelId);
        return config ? config.triggers || [] : [];
    }
    
    /**
     * 获取关卡内特效配置
     * @param levelId 关卡ID
     * @returns 特效配置列表
     */
    getEffectConfigs(levelId: number): any[] {
        const config = this.getLevelInsideConfig(levelId);
        return config ? config.effects || [] : [];
    }
    
    /**
     * 获取关卡背景配置
     * @param levelId 关卡ID
     * @returns 背景配置
     */
    getBackgroundConfig(levelId: number): any {
        const config = this.getLevelInsideConfig(levelId);
        return config ? config.background : null;
    }
    
    /**
     * 获取关卡音效配置
     * @param levelId 关卡ID
     * @returns 音效配置
     */
    getAudioConfig(levelId: number): any {
        const config = this.getLevelInsideConfig(levelId);
        return config ? config.audio : null;
    }
    
    /**
     * 获取关卡时间限制
     * @param levelId 关卡ID
     * @returns 时间限制（秒）
     */
    getTimeLimit(levelId: number): number {
        const config = this.getLevelInsideConfig(levelId);
        return config ? config.timeLimit || 0 : 0;
    }
    
    /**
     * 获取关卡胜利条件
     * @param levelId 关卡ID
     * @returns 胜利条件配置
     */
    getVictoryConditions(levelId: number): any {
        const config = this.getLevelInsideConfig(levelId);
        return config ? config.victoryConditions : null;
    }
    
    /**
     * 获取关卡失败条件
     * @param levelId 关卡ID
     * @returns 失败条件配置
     */
    getFailureConditions(levelId: number): any {
        const config = this.getLevelInsideConfig(levelId);
        return config ? config.failureConditions : null;
    }
    
    /**
     * 检查关卡是否有特殊规则
     * @param levelId 关卡ID
     * @returns 是否有特殊规则
     */
    hasSpecialRules(levelId: number): boolean {
        const config = this.getLevelInsideConfig(levelId);
        return config ? !!config.specialRules : false;
    }
    
    /**
     * 获取关卡特殊规则
     * @param levelId 关卡ID
     * @returns 特殊规则配置
     */
    getSpecialRules(levelId: number): any {
        const config = this.getLevelInsideConfig(levelId);
        return config ? config.specialRules : null;
    }
    
    /**
     * 获取关卡难度系数
     * @param levelId 关卡ID
     * @returns 难度系数
     */
    getDifficultyMultiplier(levelId: number): number {
        const config = this.getLevelInsideConfig(levelId);
        return config ? config.difficultyMultiplier || 1.0 : 1.0;
    }
    
    /**
     * 获取关卡推荐等级
     * @param levelId 关卡ID
     * @returns 推荐等级
     */
    getRecommendedLevel(levelId: number): number {
        const config = this.getLevelInsideConfig(levelId);
        return config ? config.recommendedLevel || 1 : 1;
    }
}

export { LvInsideCfgReader };
